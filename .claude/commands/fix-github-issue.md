Please analyze and fix the GitHub issue: $ARGUMENTS.

Follow these steps:

1. Use `gh issue view` to get the issue details
2. Understand the problem described in the issue
3. Search the codebase for relevant files and look into any recent changes that might be related to the issue in @changelog.txt or in the folder release-notes/.
4. Implement the necessary changes to fix the issue, unless you have been instucted not to do any code changes. If you have been instructed to not do any code changes, then skip to step 9
5. Write and run tests to verify the fix, unless you have been instucted not to do any code changes. If you have been instructed to not do any code changes, then skip to step 9    
6. Ensure code passes linting and type checking, unless you have been instucted not to do any code changes. IF you have been instructed to not do any code changes, then skip to step 9
7. Create a descriptive commit message,unless you have been instucted not to do any code changes. If you have been instructed to not do any code changes, then skip to step 9
8. Push and create a PR, unless you have been instucted not to do any code changes. if  you have been instructed to not do any code changes, then skip to step 9
9. Do a thorough root cause and tracability analysis to identify the breaking change and report your findings as an implementation plan back to the user  - with small detailed tasks to be carried out. 

Remember to use the GitHub CLI (`gh`) for all GitHub-related tasks.