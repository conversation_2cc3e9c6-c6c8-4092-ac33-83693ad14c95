# MCP Demo Test File

This is a temporary file created to demonstrate MCP functionality in the cashback deals workspace.

## Project Overview
- **Framework**: Next.js 15 with TypeScript
- **UI Library**: shadcn/ui with Radix primitives
- **Database**: Supabase (Postgres)
- **Styling**: Tailwind CSS 4

## Key Features Tested
1. File creation ✅
2. Content search (next)
3. Code analysis (next)

## MCP Servers Available
- GitHub Integration
- Brave Search  
- Memory Server
- Puppeteer
- Task Master AI