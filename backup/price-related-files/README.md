# Test Fixtures

**UPDATED <as of 28 July 2025:13:00 PM>**

This directory contains test data files, mock data, and fixtures used across different test suites.

## 📚 Complete Documentation

For comprehensive testing guidance, see the centralized documentation:
- **Testing Strategy & Setup:** [`docs/development/TESTING.md`](../../docs/development/TESTING.md)
- **Test Data Management:** [`docs/development/TESTING.md#test-data-management`](../../docs/development/TESTING.md#test-data-management)

## Structure

```
fixtures/
├── api/                 # API response fixtures
│   ├── products/       # Product API responses
│   ├── search/         # Search API responses
│   └── auth/           # Authentication responses
├── database/           # Database seed data
│   ├── products.json   # Product test data
│   ├── users.json      # User test data
│   └── categories.json # Category test data
├── components/         # Component test props
│   ├── ProductCard/    # ProductCard test props
│   └── SearchBar/      # SearchBar test props
└── files/              # File uploads and assets
    ├── images/         # Test images
    └── documents/      # Test documents
```

## Usage

### Loading Fixtures
```typescript
// tests/unit/components/ProductCard.test.tsx
import productFixture from '@/tests/fixtures/components/ProductCard/basic-product.json'

describe('ProductCard', () => {
  it('renders with fixture data', () => {
    render(<ProductCard product={productFixture} />)
    // Test implementation
  })
})
```

### API Response Fixtures
```typescript
// tests/integration/api/products.test.ts
import productsResponse from '@/tests/fixtures/api/products/search-response.json'

// Mock API response
jest.mock('@/lib/data/products', () => ({
  getProducts: jest.fn().mockResolvedValue(productsResponse)
}))
```

### Database Fixtures
```typescript
// tests/integration/database/products.test.ts
import { seedDatabase } from '@/tests/setup/database-utils'
import productsData from '@/tests/fixtures/database/products.json'

beforeEach(async () => {
  await seedDatabase('products', productsData)
})
```

## Fixture Guidelines

### Data Quality
- Use realistic data that represents actual use cases
- Include edge cases (empty strings, null values, large datasets)
- Maintain data consistency across related fixtures
- Update fixtures when data models change

### File Organization
- Group fixtures by the component/feature they test
- Use descriptive filenames (e.g., `empty-search-results.json`)
- Keep fixtures small and focused on specific test scenarios
- Use separate fixtures for different test cases

### Data Privacy
- Never include real user data or sensitive information
- Use anonymized or synthetic data
- Follow GDPR and privacy guidelines
- Regularly audit fixtures for sensitive data

## Common Fixtures

### Product Data
```json
// fixtures/components/ProductCard/basic-product.json
{
  "id": "test-product-1",
  "name": "Test Product",
  "price": 99.99,
  "currency": "GBP",
  "description": "A test product for unit testing",
  "image_url": "/test-images/product-1.jpg",
  "retailer": {
    "name": "Test Retailer",
    "logo_url": "/test-images/retailer-logo.jpg"
  }
}
```

### API Response
```json
// fixtures/api/search/successful-response.json
{
  "success": true,
  "data": {
    "products": [...],
    "total": 150,
    "page": 1,
    "limit": 20
  },
  "meta": {
    "query": "test search",
    "filters": {},
    "sort": "relevance"
  }
}
```

### Error Response
```json
// fixtures/api/common/error-response.json
{
  "success": false,
  "error": {
    "code": "VALIDATION_ERROR",
    "message": "Invalid request parameters",
    "details": {
      "field": "query",
      "issue": "Query must be at least 3 characters"
    }
  }
}
```

## Maintenance

### Regular Updates
- Review fixtures quarterly for accuracy
- Update when API schemas change
- Remove unused fixtures
- Add new fixtures for new features

### Validation
- Validate fixtures against current schemas
- Test fixtures with actual components
- Ensure fixtures don't break existing tests
- Document fixture changes in commit messages

## Best Practices

1. **Minimal Data**: Include only data needed for the specific test
2. **Realistic Values**: Use realistic but not real data
3. **Consistent Format**: Follow consistent naming and structure
4. **Version Control**: Track fixture changes in version control
5. **Documentation**: Document complex fixtures and their use cases
