#!/bin/bash

# Smoke Test Script for HMAC Authentication
# Tests authentication functionality after deployment

set -e

# Configuration
BASE_URL="${BASE_URL:-https://api.cashback-deals.com}"
PARTNER_ID="${PARTNER_ID:-sandbox-partner}"
PARTNER_SECRET="${PARTNER_SECRET:-sandbox-secret-32-chars-minimum-length-test}"
VERBOSE="${VERBOSE:-false}"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Generate HMAC signature
generate_signature() {
    local method="$1"
    local path="$2"
    local timestamp="$3"
    local body="$4"
    
    # Calculate body hash
    local body_hash=$(echo -n "$body" | sha256sum | cut -d' ' -f1)
    
    # Create message
    local message="$method\n$path\n$timestamp\n$body_hash"
    
    # Generate signature
    echo -n -e "$message" | openssl dgst -sha256 -hmac "$PARTNER_SECRET" | cut -d' ' -f2
}

# Test function
test_endpoint() {
    local test_name="$1"
    local method="$2"
    local path="$3"
    local body="$4"
    local expected_status="$5"
    local use_auth="${6:-true}"
    
    log_info "Testing: $test_name"
    
    local timestamp=$(date +%s)
    local url="$BASE_URL$path"
    
    # Prepare curl command
    local curl_cmd="curl -s -w '%{http_code}' -o /tmp/response.json"
    
    if [ "$use_auth" = "true" ]; then
        local signature=$(generate_signature "$method" "$path" "$timestamp" "$body")
        curl_cmd="$curl_cmd -H 'X-Signature: sha256=$signature'"
        curl_cmd="$curl_cmd -H 'X-Timestamp: $timestamp'"
        curl_cmd="$curl_cmd -H 'X-Partner-ID: $PARTNER_ID'"
        curl_cmd="$curl_cmd -H 'X-Version: 1.0'"
    fi
    
    curl_cmd="$curl_cmd -H 'Content-Type: application/json'"
    
    if [ "$method" = "POST" ] && [ -n "$body" ]; then
        curl_cmd="$curl_cmd -d '$body'"
    fi
    
    curl_cmd="$curl_cmd -X $method '$url'"
    
    if [ "$VERBOSE" = "true" ]; then
        log_info "Command: $curl_cmd"
    fi
    
    # Execute request
    local status_code=$(eval $curl_cmd)
    local response=$(cat /tmp/response.json)
    
    if [ "$VERBOSE" = "true" ]; then
        log_info "Response: $response"
    fi
    
    # Check status code
    if [ "$status_code" = "$expected_status" ]; then
        log_success "$test_name - Status: $status_code ✓"
        return 0
    else
        log_error "$test_name - Expected: $expected_status, Got: $status_code ✗"
        log_error "Response: $response"
        return 1
    fi
}

# Main test suite
main() {
    log_info "Starting HMAC Authentication Smoke Tests"
    log_info "Base URL: $BASE_URL"
    log_info "Partner ID: $PARTNER_ID"
    echo ""
    
    local failed_tests=0
    local total_tests=0
    
    # Test 1: No authentication (should fail)
    total_tests=$((total_tests + 1))
    if ! test_endpoint "No Authentication" "GET" "/api/search?q=test" "" "401" "false"; then
        failed_tests=$((failed_tests + 1))
    fi
    
    # Test 2: Valid HMAC authentication (should succeed)
    total_tests=$((total_tests + 1))
    if ! test_endpoint "Valid HMAC Auth" "GET" "/api/search?q=test" "" "200" "true"; then
        failed_tests=$((failed_tests + 1))
    fi
    
    # Test 3: Search suggestions with auth
    total_tests=$((total_tests + 1))
    if ! test_endpoint "Search Suggestions" "GET" "/api/search/suggestions?q=lap" "" "200" "true"; then
        failed_tests=$((failed_tests + 1))
    fi
    
    # Test 4: Search more with auth
    total_tests=$((total_tests + 1))
    if ! test_endpoint "Search More" "GET" "/api/search/more?q=laptop&page=2" "" "200" "true"; then
        failed_tests=$((failed_tests + 1))
    fi
    
    # Test 5: Invalid signature (should fail)
    total_tests=$((total_tests + 1))
    PARTNER_SECRET="wrong-secret" 
    if ! test_endpoint "Invalid Signature" "GET" "/api/search?q=test" "" "401" "true"; then
        failed_tests=$((failed_tests + 1))
    fi
    # Restore correct secret
    PARTNER_SECRET="${PARTNER_SECRET:-sandbox-secret-32-chars-minimum-length-test}"
    
    # Test 6: Expired timestamp (should fail)
    total_tests=$((total_tests + 1))
    local old_timestamp=$(($(date +%s) - 400)) # 400 seconds ago
    local old_signature=$(generate_signature "GET" "/api/search?q=test" "$old_timestamp" "")
    local curl_cmd="curl -s -w '%{http_code}' -o /tmp/response.json"
    curl_cmd="$curl_cmd -H 'X-Signature: sha256=$old_signature'"
    curl_cmd="$curl_cmd -H 'X-Timestamp: $old_timestamp'"
    curl_cmd="$curl_cmd -H 'X-Partner-ID: $PARTNER_ID'"
    curl_cmd="$curl_cmd -H 'Content-Type: application/json'"
    curl_cmd="$curl_cmd -X GET '$BASE_URL/api/search?q=test'"
    
    local status_code=$(eval $curl_cmd)
    if [ "$status_code" = "401" ]; then
        log_success "Expired Timestamp - Status: $status_code ✓"
    else
        log_error "Expired Timestamp - Expected: 401, Got: $status_code ✗"
        failed_tests=$((failed_tests + 1))
    fi
    
    # Test 7: POST request with body
    total_tests=$((total_tests + 1))
    local post_body='{"query":"laptop","filters":{"brand":"samsung"}}'
    if ! test_endpoint "POST with Body" "POST" "/api/search" "$post_body" "200" "true"; then
        failed_tests=$((failed_tests + 1))
    fi
    
    echo ""
    log_info "Test Results:"
    log_info "Total Tests: $total_tests"
    log_info "Passed: $((total_tests - failed_tests))"
    
    if [ $failed_tests -eq 0 ]; then
        log_success "All tests passed! ✓"
        exit 0
    else
        log_error "Failed Tests: $failed_tests ✗"
        exit 1
    fi
}

# Help function
show_help() {
    echo "HMAC Authentication Smoke Test Script"
    echo ""
    echo "Usage: $0 [OPTIONS]"
    echo ""
    echo "Options:"
    echo "  -h, --help              Show this help message"
    echo "  -v, --verbose           Enable verbose output"
    echo "  -u, --url URL           Set base URL (default: https://api.cashback-deals.com)"
    echo "  -p, --partner-id ID     Set partner ID (default: sandbox-partner)"
    echo "  -s, --secret SECRET     Set partner secret"
    echo ""
    echo "Environment Variables:"
    echo "  BASE_URL               Base API URL"
    echo "  PARTNER_ID             Partner ID for authentication"
    echo "  PARTNER_SECRET         Partner secret for HMAC signing"
    echo "  VERBOSE                Enable verbose output (true/false)"
    echo ""
    echo "Examples:"
    echo "  $0                                    # Run with defaults"
    echo "  $0 --verbose                          # Run with verbose output"
    echo "  $0 --url https://staging.example.com # Test staging environment"
    echo "  VERBOSE=true $0                       # Use environment variable"
}

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        -h|--help)
            show_help
            exit 0
            ;;
        -v|--verbose)
            VERBOSE=true
            shift
            ;;
        -u|--url)
            BASE_URL="$2"
            shift 2
            ;;
        -p|--partner-id)
            PARTNER_ID="$2"
            shift 2
            ;;
        -s|--secret)
            PARTNER_SECRET="$2"
            shift 2
            ;;
        *)
            log_error "Unknown option: $1"
            show_help
            exit 1
            ;;
    esac
done

# Check dependencies
if ! command -v curl &> /dev/null; then
    log_error "curl is required but not installed"
    exit 1
fi

if ! command -v openssl &> /dev/null; then
    log_error "openssl is required but not installed"
    exit 1
fi

if ! command -v sha256sum &> /dev/null; then
    log_error "sha256sum is required but not installed"
    exit 1
fi

# Run tests
main
