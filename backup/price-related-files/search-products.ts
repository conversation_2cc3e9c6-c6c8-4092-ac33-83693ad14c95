/**
 * Product Search Utility
 * 
 * This script provides a command-line interface to search products in the Supabase database.
 * It's useful for debugging and manual database queries during development.
 * 
 * Prerequisites:
 * - Node.js with TypeScript support
 * - Environment variables set in .env.local:
 *   - NEXT_PUBLIC_SUPABASE_URL
 *   - NEXT_PUBLIC_SUPABASE_ANON_KEY
 * 
 * Usage:
 * 1. Run with npx: `npx ts-node scripts/search-products.ts`
 * 2. The script will search for products matching the query 'series' by default
 * 3. To search for a different term, modify the `query` variable at the bottom of the file
 * 
 * Dependencies:
 * - @supabase/supabase-js: ^2.47.13
 * - dotenv: ^16.4.7
 * - typescript: ^5.7.3
 * - ts-node: ^10.9.2 (for direct execution)
 */

import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

// Load environment variables from .env.local
dotenv.config({ path: '.env.local' });

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!;
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!;

if (!supabaseUrl || !supabaseAnonKey) {
  console.error('Missing Supabase environment variables');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseAnonKey);

async function searchProducts(query: string) {
  console.log(`Searching for products with query: "${query}"`);
  
  const { data, error, count } = await supabase
    .from('products')
    .select(`
      *,
      brand:brand_id (id, name, slug, logo_url, description),
      promotion:promotion_id (*)
    `, { count: 'exact' })
    .ilike('name', `%${query}%`)
    .limit(20);

  if (error) {
    console.error('Error searching products:', error);
    return null;
  }

  return {
    count,
    products: data || []
  };
}

// Run the search with the specified query
// Change this value to search for different products
const query = 'series';
searchProducts(query)
  .then((result) => {
    if (result) {
      console.log('Search Results:');
      console.log(`Total products found: ${result.count}`);
      console.log('First few products:');
      console.log(JSON.stringify(result.products.slice(0, 3), null, 2));
      console.log(`\nSQL Query executed:`);
      console.log(`SELECT * FROM products WHERE name ILIKE '%${query}%'`);
    }
  })
  .catch((error) => {
    console.error('Error:', error);
  });
