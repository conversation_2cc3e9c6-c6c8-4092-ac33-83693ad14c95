# Component Dependency Matrix

*Auto-generated documentation mapping pages to components, libraries, and dependencies. Last updated: 20th July 2025*

## Overview

This document provides a comprehensive mapping of user-facing pages to their technical components, key dependencies, and architectural patterns. It serves as a quick reference for developers to understand application structure and component relationships.

## Page-to-Component Mapping

### Core User Pages

| Page / Route | Main Components | Key Libraries | Primary Files | Rendering |
|--------------|----------------|---------------|---------------|-----------|
| **Homepage** (`/`) | `HomePageClient`, `FeaturedProductCard`, `FeaturedPromotionCard`, `BrandLogo` | `framer-motion`, `@supabase/supabase-js`, `lucide-react` | `src/app/page.tsx`, `src/components/pages/HomePageClient.tsx` | SSG + ISR |
| **Products Listing** (`/products`) | `ProductsContent`, `ProductGrid`, `ProductCard`, `Pagination`, `PaginationInfo` | `@tanstack/react-query`, `zod`, `@supabase/supabase-js` | `src/app/products/page.tsx`, `src/app/products/components/ProductsContent.tsx` | SSR |
| **Product Detail** (`/products/[id]`) | `ProductPageClient`, `ProductInfo`, `PricingOffersSection`, `ProductSpecifications`, `ProductDetailsSection`, `SimilarProducts` | `framer-motion`, `next/image`, `@supabase/supabase-js` | `src/app/products/[id]/page.tsx`, `src/components/pages/ProductPageClient.tsx`, `src/app/products/components/PricingOffersSection.tsx`, `src/app/products/components/ProductSpecifications.tsx` | SSG + ISR |
| **Brands Listing** (`/brands`) | `BrandsClient`, `AlphabetNavigationClient`, `BrandGroup`, `SearchInput` | `@tanstack/react-query`, `lucide-react` | `src/app/brands/page.tsx`, `src/app/brands/BrandsClient.tsx` | SSR |
| **Brand Detail** (`/brands/[id]`) | `BrandClient`, `BrandHeader`, `ProductGrid`, `Pagination` | `@supabase/supabase-js`, `framer-motion` | `src/app/brands/[id]/page.tsx`, `src/app/brands/[id]/BrandClient.tsx` | SSG + ISR |
| **Search** (`/search`) | `SearchPageClient`, `SearchBar`, `SearchSuggestions`, `ProductGrid` | `@tanstack/react-query`, `react-hook-form`, `zod` | `src/app/search/page.tsx`, `src/components/pages/SearchPageClient.tsx` | SSR |
| **Retailers** (`/retailers`) | `RetailersPageClient`, `RetailerCard`, `Pagination` | `@supabase/supabase-js`, `lucide-react` | `src/app/retailers/page.tsx`, `src/components/pages/RetailersPageClient.tsx` | SSR |
| **Contact** (`/contact`) | `ContactPageContent`, `ContactForm`, `Turnstile` | `react-hook-form`, `@marsidev/react-turnstile`, `zod` | `src/app/contact/page.tsx`, `src/app/contact/ContactPageContent.tsx` | SSG |
| **About** (`/about`) | `AboutPage` (client component) | `framer-motion`, `next/image` | `src/app/about/page.tsx` | Edge Runtime |

### Static/Legal Pages

| Page / Route | Main Components | Key Libraries | Primary Files | Rendering |
|--------------|----------------|---------------|---------------|-----------|
| **Privacy Policy** (`/privacy`) | `PrivacyPage` | `next/image`, `next/link` | `src/app/privacy/page.tsx` | SSG |
| **Terms of Service** (`/terms`) | `TermsPage` | `next/image`, `next/link` | `src/app/terms/page.tsx` | SSG |
| **Thank You** (`/contact/thank-you`) | `ThankYouPage` | `next/image`, `next/link` | `src/app/contact/thank-you/page.tsx` | SSG |

## Component Architecture Breakdown

### Server Components (Data Fetching)

| Component | Purpose | Data Sources | Cache Strategy |
|-----------|---------|--------------|----------------|
| `src/app/page.tsx` | Homepage server logic | `getFeaturedProducts`, `getFeaturedPromotions`, `getFeaturedBrands` | 1 hour ISR |
| `src/app/products/page.tsx` | Products listing server logic | `getProducts`, `getFilterOptions` | Server-side cache |
| `src/app/products/[id]/page.tsx` | Product detail server logic | `getProductPageData` | 30 min ISR |
| `src/app/brands/page.tsx` | Brands listing server logic | `getBrands` | Server-side cache |
| `src/app/brands/[id]/page.tsx` | Brand detail server logic | `getBrandPageData` | 1 hour ISR |
| `src/app/search/page.tsx` | Search results server logic | `searchProducts` | Short cache (5 min) |

### Client Components (Interactivity)

| Component | Purpose | State Management | Key Hooks |
|-----------|---------|------------------|-----------|
| `HomePageClient` | Homepage interactivity | Local state | `useState`, `useEffect` |
| `ProductsContent` | Products grid + pagination | URL state | `useProductsPagination`, `useRouter` |
| `ProductPageClient` | Product detail interactions | Local state | `useState`, `useCallback` |
| `BrandsClient` | Brands grid + alphabet nav | URL state | `useBrandsPagination`, `useState` |
| `BrandClient` | Brand detail + product grid | URL state | `usePagination`, `useRouter` |
| `SearchPageClient` | Search interface + results | URL state + local | `useSearchPagination`, `useDebounce` |
| `ContactPageContent` | Contact form + validation | Form state | `useForm`, `useState` |

### Reusable UI Components

| Component | Used By | Dependencies | Purpose |
|-----------|---------|--------------|----------|
| `ProductCard` | Products, Search, Homepage, Similar Products | `next/image`, `next/link`, `lucide-react` | Product display card |
| `ProductGrid` | Products, Search, Brand detail | `ProductCard` | Responsive product grid |
| `BrandLogo` | Homepage, Product detail, Brands | `next/image`, `next/link` | Brand logo display |
| `Pagination` | Products, Brands, Search, Retailers | `lucide-react` | Page navigation |
| `PaginationInfo` | Products, Brands, Search | None | Results count display |
| `SearchBar` | Header, Search page | `react-hook-form`, `lucide-react` | Search input component |
| `SearchSuggestions` | SearchBar | `@tanstack/react-query` | Auto-complete suggestions |
| `OptimizedImage` | Throughout app | `next/image`, `@sentry/nextjs` | Performance-optimized images |
| `ResilientImage` | Fallback scenarios | `next/image` | Error-resilient image component |

### Product Page Components (v15.8)

| Component | Purpose | Dependencies | Key Features |
|-----------|---------|--------------|--------------|
| `PricingOffersSection` | Dedicated pricing and offers management | `react`, `lucide-react` | Price range calculation, best offer detection, partner text generation, responsive action buttons |
| `ProductSpecifications` | Comprehensive specifications display | `react`, `lucide-react` | Automatic categorization, expandable sections, formatted lists, SEO optimization |
| `ProductInfo` | Basic product information display | `react`, `next/image`, `next/link` | Performance optimized with memoization, enhanced retailer offers display |
| `ProductDetailsSection` | Product description display | `react` | Simplified description rendering with proper formatting |

## Data Layer Dependencies

### Core Data Functions

| Module | Functions | Supabase Tables | Cache Duration |
|--------|-----------|----------------|----------------|
| `src/lib/data/products.ts` | `getProducts`, `getProduct`, `getFeaturedProducts` | `products`, `brands`, `categories`, `product_retailer_offers` | MEDIUM (30 min) |
| `src/lib/data/brands.ts` | `getBrands`, `getBrand`, `getBrandWithDetails` | `brands`, `products` | LONG (1 hour) |
| `src/lib/data/search.ts` | `searchProducts`, `getSearchSuggestions` | `products`, `brands` | SHORT (5 min) |
| `src/lib/data/retailers.ts` | `getRetailers`, `getRetailer` | `retailers`, `product_retailer_offers` | EXTENDED (24 hours) |
| `src/lib/data/promotions.ts` | `getPromotions`, `getFeaturedPromotions` | `promotions`, `brands` | MEDIUM (30 min) |

### Custom Hooks

| Hook | Purpose | Pages Using | Dependencies |
|------|---------|-------------|--------------|
| `usePagination` | Base URL pagination | All paginated pages | `next/navigation` |
| `useProductsPagination` | Products-specific pagination | Products listing | `usePagination` |
| `useBrandsPagination` | Brands-specific pagination | Brands listing | `usePagination` |
| `useSearchPagination` | Search-specific pagination | Search results | `usePagination` |
| `useDebounce` | Search input debouncing | Search components | None |
| `usePerformanceOptimization` | Web Vitals monitoring | Layout | `web-vitals` |

## API Routes Dependencies

| API Route | Purpose | Libraries | Authentication |
|-----------|---------|-----------|----------------|
| `/api/search/route.ts` | Search functionality | `zod`, `@supabase/supabase-js` | Rate limiting |
| `/api/contact/route.ts` | Contact form submission | `nodemailer`, `@marsidev/react-turnstile` | CAPTCHA + rate limiting |
| `/api/brands/[id]/route.ts` | Brand data API | `@supabase/supabase-js`, `zod` | Public |
| `/api/products/[id]/route.ts` | Product data API | `@supabase/supabase-js`, `zod` | Public |
| `/api/retailers/[id]/route.ts` | Retailer data API | `@supabase/supabase-js`, `zod` | Public |

## Security & Validation

### Input Validation

| Component/API | Validation Library | Schema Location | Sanitization |
|---------------|-------------------|-----------------|--------------|
| Search API | `zod` | `src/lib/validation/schemas.ts` | Query length limits |
| Contact Form | `zod` + `react-hook-form` | `src/lib/validation/schemas.ts` | `isomorphic-dompurify` |
| Product API | `zod` | `src/lib/validation/schemas.ts` | ID validation |
| Brand API | `zod` | `src/lib/validation/schemas.ts` | Slug validation |

### Authentication & Rate Limiting

| Feature | Implementation | Libraries | Configuration |
|---------|----------------|-----------|---------------|
| Rate Limiting | Custom middleware | `ipaddr.js` | API route protection |
| CAPTCHA | Contact form | `@marsidev/react-turnstile` | Cloudflare Turnstile |
| XSS Protection | Input sanitization | `isomorphic-dompurify` | All user inputs |
| CORS | Next.js headers | Built-in | `next.config.js` |

## SEO & Performance

### Breadcrumb Navigation

#### Dynamic Routing Strategy

**Key Features:**
- Search-based category and brand routing
- Flexible navigation generation
- SEO-compatible structured data

```typescript
// Breadcrumb generation function
function generateBreadcrumbs(page: string, params: any) {
  switch(page) {
    case 'product':
      return [
        { label: 'Home', href: '/' },
        { 
          label: params.category, 
          href: `/search?category=${params.categorySlug}` 
        },
        { label: params.productName, href: null }
      ];
    case 'brand':
      return [
        { label: 'Home', href: '/' },
        { 
          label: 'Brands', 
          href: '/search?type=brand' 
        },
        { label: params.brandName, href: null }
      ];
  }
}
```

#### Structured Data Components

| Page Type | Structured Data Components | Schema Types | Routing Strategy |
|-----------|---------------------------|--------------|------------------|
| Homepage | `WebSiteStructuredData`, `OrganizationStructuredData` | WebSite, Organization | Static |
| Product Detail | `ProductStructuredData`, `BreadcrumbStructuredData` | Product, BreadcrumbList | Search-based category |
| Brand Detail | `BrandStructuredData`, `BreadcrumbStructuredData` | Brand, BreadcrumbList | Search-based brand |
| Search Results | `SearchResultsStructuredData` | SearchResultsPage | Dynamic filters |
| Contact | `ContactStructuredData` | ContactPage | Static |
| Homepage | `WebSiteStructuredData`, `OrganizationStructuredData` | WebSite, Organization |
| Product Detail | `ProductStructuredData`, `BreadcrumbStructuredData` | Product, BreadcrumbList |
| Brand Detail | `BrandStructuredData`, `BreadcrumbStructuredData` | Brand, BreadcrumbList |
| Search Results | `SearchResultsStructuredData` | SearchResultsPage |
| Contact | `ContactStructuredData` | ContactPage |

### Performance Optimization

| Feature | Implementation | Libraries | Impact |
|---------|----------------|-----------|--------|
| Image Optimization | `OptimizedImage`, `ResilientImage` | `next/image` | Core Web Vitals |
| Bundle Splitting | Webpack config | Built-in | Load time |
| Caching | Multi-tier strategy | `unstable_cache` | Response time |
| Web Vitals | Performance monitoring | `web-vitals`, `@sentry/nextjs` | User experience |

## Styling & Design System

### Core Styling

| Component Category | Styling Approach | Dependencies | Customization |
|-------------------|------------------|--------------|---------------|
| Layout Components | Tailwind CSS classes | `tailwindcss`, `tailwind-merge` | Custom design tokens |
| UI Components | shadcn/ui + Tailwind | `@radix-ui/*`, `class-variance-authority` | Theme variants |
| Animation | Framer Motion | `framer-motion` | Custom transitions |
| Icons | Lucide React | `lucide-react` | Consistent icon set |

### Design Tokens

| Token Category | Implementation | Usage |
|----------------|----------------|-------|
| Colors | Tailwind config | Brand colors, semantic colors |
| Typography | Tailwind config | Font families, sizes, weights |
| Spacing | Tailwind config | Consistent spacing scale |
| Breakpoints | Tailwind config | Responsive design |

## Testing Architecture

### Component Testing

| Test Type | Framework | Files Tested | Coverage Target |
|-----------|-----------|--------------|-----------------|
| Unit Tests | Jest + React Testing Library | All components in `src/components/` | 80%+ |
| Integration Tests | Jest | API routes, data layer functions | 90%+ |
| E2E Tests | Playwright | Critical user flows | Key scenarios |

### Test Environment

| Environment | Authentication | Database | Purpose |
|-------------|----------------|----------|----------|
| Development | Disabled | Local Supabase | Feature development |
| Test | Mock tokens | Test database | Automated testing |
| Staging | Enabled | Staging database | Pre-production testing |
| Production | Enabled | Production database | Live application |

## Dependency Overview

### Framework Dependencies (Production)

| Category | Packages | Purpose |
|----------|----------|---------|
| Core Framework | `next@15.3.5`, `react@19.1.0`, `react-dom@19.1.0` | Application foundation |
| Database | `@supabase/supabase-js@2.50.4`, `@supabase/ssr@0.6.1` | Data layer |
| UI Library | `@radix-ui/*`, `@shadcn/ui@0.0.4`, `lucide-react@0.525.0` | Component library |
| Styling | `tailwindcss@4.1.11`, `tailwind-merge@3.3.1`, `framer-motion@12.23.1` | Design system |
| Forms | `react-hook-form@7.60.0`, `@hookform/resolvers@5.1.1` | Form management |
| Validation | `zod@4.0.0` | Schema validation |
| Security | `isomorphic-dompurify@2.26.0`, `@marsidev/react-turnstile@1.1.0` | Security measures |
| State Management | `@tanstack/react-query@5.82.0` | Server state |
| Monitoring | `@sentry/nextjs@9.36.0`, `web-vitals@5.0.3` | Error tracking & performance |

### Development Dependencies

| Category | Packages | Purpose |
|----------|----------|---------|
| Build Tools | `typescript@5.8.3`, `@tailwindcss/postcss@4.1.11`, `autoprefixer@10.4.21` | Build pipeline |
| Testing | `jest@30.0.4`, `@testing-library/react@16.3.0`, `@playwright/test@1.53.2` | Quality assurance |
| Linting | `eslint@9.30.1`, `eslint-config-next@15.3.5` | Code quality |
| Types | `@types/node@24.0.12`, `@types/react@19.1.8`, `@types/react-dom@19.1.6` | Type safety |

## Usage Patterns

### Common Development Tasks

| Task | Components Involved | Process |
|------|-------------------|---------|
| **Add New Product Feature** | Server component → Data layer → Client component → UI components | 1. Update data schema 2. Add server logic 3. Create client interactivity 4. Add UI components |
| **Implement Search Feature** | SearchAPI → SearchPageClient → SearchBar → SearchSuggestions | 1. API route 2. Client component 3. Form components 4. Results display |
| **Add Authentication** | Layout → All protected pages → Auth components | 1. Auth provider 2. Protected routes 3. Login/logout UI |
| **Performance Optimization** | All components → Caching → Image optimization → Bundle analysis | 1. Identify bottlenecks 2. Add caching 3. Optimize images 4. Split bundles |

### Debugging Guide

| Issue Type | Components to Check | Tools |
|------------|-------------------|-------|
| **Page Load Issues** | Server components, data layer, Supabase connection | Next.js dev tools, browser network tab |
| **Styling Problems** | UI components, Tailwind classes, design tokens | Browser dev tools, Tailwind IntelliSense |
| **State Management** | Client components, pagination hooks, URL state | React dev tools, console logging |
| **Performance Issues** | Image components, bundle size, caching | Lighthouse, Web Vitals, bundle analyzer |

## Next Steps / Maintenance

- [ ] Update component mapping when new pages are added
- [ ] Monitor bundle size impact of new dependencies
- [ ] Review and update cache strategies quarterly
- [ ] Document any new architectural patterns
- [ ] Maintain test coverage targets
- [ ] Update dependency versions regularly
- [ ] Add performance budgets for key pages
- [ ] Document component API contracts
- [ ] Create component usage examples
- [ ] Establish component deprecation process

---

**Last Updated**: 20th July 2025  
**Version**: 1.0  
**Next Review**: October 2025