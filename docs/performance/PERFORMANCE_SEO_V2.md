# Performance & SEO Optimization Guide V2

*This is the updated and accurate documentation for performance optimization and SEO implementation. Last updated: 08th August 2025*


> v15.8.2 Update: Brands pages now emit self-referencing canonicals and render JSON-LD via components (BreadcrumbList on index; Brand + BreadcrumbList on detail). AI bot policy added (agent-specific robots rules and new /ai.txt). Playwright E2E tests added under tests/e2e/seo to validate canonicals, structured data, and AI policy endpoints.

## Overview

The RebateRay platform implements comprehensive performance optimization and SEO strategies to ensure fast loading times, excellent user experience, and strong search engine visibility. This document reflects the **actual current state** of the implementation as of July 2025.

## Infrastructure Architecture ✅ CORRECTED

### Actual Deployment Stack
```mermaid
graph TB
    User[User Request] --> CF[Cloudflare CDN + Security]
    CF --> Amplify[AWS Amplify Console]
    Amplify --> App[Next.js 15.3.5 App]
    App --> Data[Data Layer Cache]
    Data --> Supabase[Supabase Database]

    subgraph "Cache Layers"
        CF -.-> C1[Static Assets<br/>1 year]
        Amplify -.-> C2[Pages<br/>ISR/SSG]
        App -.-> C3[API Routes<br/>30 minutes]
        Data -.-> C4[Database Queries<br/>5-30 minutes]
    end
```

**Key Infrastructure Facts:**
- **Hosting**: AWS Amplify Console (NOT Vercel)
- **CDN/Security**: Cloudflare proxy with TLS termination
- **Database**: Supabase with caching
- **Framework**: Next.js 15.3.5 + React 19.1.0

## Core Web Vitals Targets ✅ UPDATED 2025

### Performance Metrics & Current Thresholds

| Metric | 2025 Target | Implementation Status | Monitoring |
|--------|-------------|---------------------|------------|
| **LCP (Largest Contentful Paint)** | < 2.5s | ✅ Optimized | next/image, caching |
| **INP (Interaction to Next Paint)** | < 200ms | ⚠️ Needs monitoring | **Replaces FID in 2025** |
| **CLS (Cumulative Layout Shift)** | < 0.1 | ✅ Implemented | Image dimensions, layouts |
| **FCP (First Contentful Paint)** | < 1.8s | ✅ Optimized | Server-side rendering |
| **TTFB (Time to First Byte)** | < 800ms | ✅ Cached | Edge caching, SSG |

### Web Vitals Monitoring Implementation ⚠️ PARTIALLY IMPLEMENTED

**Current Status**: WebVitals component exists but is **DISABLED in development mode**

```typescript
// src/components/performance/WebVitals.tsx - ACTUAL IMPLEMENTATION
'use client'

export function WebVitals({
    debug = false,
    reportToAnalytics = true
}: WebVitalsConfig) {
    useEffect(() => {
        // 🚨 CURRENTLY DISABLED IN DEVELOPMENT
        if (process.env.NODE_ENV !== 'production') {
            if (debug) {
                console.log('WebVitals is disabled in development mode');
            }
            return;
        }

        // Only loads in production - tracks INP (2025 update)
        import('web-vitals').then(({ onCLS, onFCP, onLCP, onTTFB }) => {
            onCLS(handleMetric);  // Cumulative Layout Shift
            onFCP(handleMetric);  // First Contentful Paint
            onLCP(handleMetric);  // Largest Contentful Paint
            onTTFB(handleMetric); // Time to First Byte
            // Note: INP tracking needs to be added for 2025 compliance
        });
    }, [debug, reportToAnalytics]);
}
```

## Caching Strategy ✅ IMPLEMENTED

### Multi-Layer Caching Architecture

```typescript
// src/lib/cache.ts - ACTUAL IMPLEMENTATION
export const CACHE_DURATIONS = {
  SHORT: 300,    // 5 minutes - frequently changing data
  MEDIUM: 1800,  // 30 minutes - moderately stable data
  LONG: 3600,    // 1 hour - stable data
  EXTENDED: 86400 // 24 hours - very stable data
} as const

export const CACHE_TAGS = {
  PRODUCTS: 'products',
  BRANDS: 'brands',
  SEARCH: 'search',
  FEATURED: 'featured',
  PROMOTIONS: 'promotions',
  RETAILERS: 'retailers',
} as const

export function createCachedFunction<T extends (...args: any[]) => Promise<any>>(
  fn: T,
  config: CacheConfig
): T {
  // Skips caching in test environment
  if (process.env.NODE_ENV === 'test') {
    return fn
  }

  return unstable_cache(
    fn,
    [config.key],
    {
      revalidate: config.revalidate || CACHE_DURATIONS.MEDIUM,
      tags: config.tags || [],
    }
  ) as T
}
```

### Cache Headers Configuration (AWS Amplify)

```javascript
// next.config.js - ACTUAL IMPLEMENTATION
async headers() {
  return [
    // API routes caching
    {
      source: '/api/(.*)',
      headers: [
        {
          key: 'Cache-Control',
          value: 'public, s-maxage=1800, stale-while-revalidate=3600'
        },
      ],
    },
    // Security headers via Cloudflare + Next.js
    {
      source: '/(.*)',
      headers: getSecurityHeaders(),
    }
  ];
}
```

## Image Optimization ✅ COMPREHENSIVE

### Enhanced OptimizedImage Component (More Advanced Than Documented)

The actual implementation is **significantly more sophisticated** than documented:

```typescript
// src/components/ui/OptimizedImage.tsx - ACTUAL IMPLEMENTATION
export function OptimizedImage({
    src, alt, width, height, fill = false, priority = false,
    productName, brandName, imageType = 'product', imageIndex, totalImages
}: OptimizedImageProps) {
    // ✅ Automatic alt text generation based on context
    const generateAltText = (): string => {
        switch (imageType) {
            case 'product':
                if (productName && brandName) {
                    const imagePosition = imageIndex !== undefined && totalImages !== undefined
                        ? ` - Image ${imageIndex + 1} of ${totalImages}`
                        : '';
                    return `${productName} by ${brandName}${imagePosition}`;
                }
                return productName || 'Product image';
            case 'brand-logo':
                return brandName ? `${brandName} logo` : 'Brand logo';
            // ... more sophisticated alt text generation
        }
    };

    // ✅ Responsive sizes based on image type
    const getResponsiveSizes = (): string => {
        switch (imageType) {
            case 'hero': return "(max-width: 768px) 100vw, (max-width: 1200px) 80vw, 60vw";
            case 'product': return "(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 40vw";
            case 'thumbnail': return "(max-width: 768px) 25vw, (max-width: 1200px) 15vw, 10vw";
            // ... more responsive configurations
        }
    };
}

// ✅ Specialized components for common use cases
export function ProductImage(props: Omit<OptimizedImageProps, 'imageType'>) {
    return <OptimizedImage {...props} imageType="product" />;
}
export function BrandLogo(props: Omit<OptimizedImageProps, 'imageType'>) {
    return <OptimizedImage {...props} imageType="brand-logo" />;
}
```

### Next.js Image Configuration (Production Ready)

```javascript
// next.config.js - ACTUAL IMPLEMENTATION
module.exports = {
    images: {
        remotePatterns: [
            { protocol: 'https', hostname: '*.supabase.co' },
            { protocol: 'https', hostname: '*.amazonaws.com' },
            { protocol: 'https', hostname: '*.cloudfront.net' },
            { protocol: 'https', hostname: 'images.samsung.com' },
        ],
        formats: ['image/webp', 'image/avif'], // Modern formats
        deviceSizes: [640, 750, 828, 1080, 1200, 1920, 2048, 3840],
        imageSizes: [16, 32, 48, 64, 96, 128, 256, 384],
        minimumCacheTTL: 60 * 60 * 24 * 365, // 1 year cache
        quality: 85,
        unoptimized: false,
    },
}
```

## SEO Implementation ✅ COMPREHENSIVE

### Metadata Management (Simplified but Functional)

```typescript
// src/lib/metadata-utils.ts - ACTUAL IMPLEMENTATION
export const siteConfig = {
  name: 'RebateRay', // ✅ CORRECT (not "Cashback Deals")
  description: 'Discover and compare cashback deals and rebates from top brands in the UK.',
  url: env.NEXT_PUBLIC_SITE_URL || 'https://4-2.d3q274urye85k3.amplifyapp.com/', // ✅ AWS Amplify URL
};

export function constructMetadata({
  title,
  description,
  image,
  noIndex = false,
  pathname,
  openGraph,
}: {
  title?: string;
  description?: string;
  image?: string;
  noIndex?: boolean;
  pathname?: string;
  openGraph?: Record<string, any>;
}): Metadata {
  const metaTitle = title
    ? `${title} | ${siteConfig.name}`
    : `${siteConfig.name} - Find the Best Rebates and Cashback Reward Deals`;

  return {
    title: metaTitle,
    description: description || siteConfig.description,
    metadataBase: new URL(siteConfig.url),
    openGraph: {
      title: metaTitle,
      description: description || siteConfig.description,
      url: pathname ? `${siteConfig.url}${pathname}` : siteConfig.url,
      siteName: siteConfig.name,
      images: image ? [{ url: image }] : undefined,
      type: 'website',
      ...(openGraph || {}),
    },
    twitter: {
      card: 'summary_large_image',
      title: metaTitle,
      description: description || siteConfig.description,
      images: image ? [image] : undefined,
    },
    robots: {
      index: !noIndex,
      follow: !noIndex,
    },
    alternates: {
      canonical: pathname ? `${siteConfig.url}${pathname}` : siteConfig.url,
    },
  };
}
```

### Structured Data Implementation ✅ VERY COMPREHENSIVE

The actual StructuredData implementation is **extremely sophisticated**:

```typescript
// src/components/seo/StructuredData.tsx - ACTUAL IMPLEMENTATION
export const ProductStructuredData: React.FC<ProductStructuredDataProps> = ({
  product, retailerOffers, fallbackPurchaseEndDate
}) => {
  // ✅ Enhanced structured data with rich snippet optimization
  const structuredData = {
    '@context': 'https://schema.org',
    '@type': 'Product',
    name: product.name,
    description: product.description || `${product.name} with cashback offer`,
    image: imageUrls.length > 0 ? imageUrls : undefined,
    sku: product.modelNumber || product.id,
    brand: product.brand ? {
      '@type': 'Brand',
      name: product.brand.name,
      logo: product.brand.logoUrl,
    } : undefined,

    // ✅ Multiple retailer offers with pricing
    offers: offers.length > 0 ? offers.map(offer => ({
      '@type': 'Offer',
      price: offer.price.toString(),
      priceCurrency: 'GBP',
      availability: offer.stockStatus === 'in_stock'
        ? 'https://schema.org/InStock'
        : 'https://schema.org/OutOfStock',
      seller: {
        '@type': 'Organization',
        name: offer.retailer?.name || 'Unknown Retailer',
      }
    })) : undefined,

    // ✅ AggregateOffer for multiple retailers (improves rich snippets)
    ...(offers.length > 1 && minPrice && maxPrice ? {
      aggregateOffer: {
        '@type': 'AggregateOffer',
        lowPrice: minPrice.toString(),
        highPrice: maxPrice.toString(),
        priceCurrency: 'GBP',
        offerCount: offers.length.toString(),
      }
    } : {}),
  };

  return (
    <script
      type="application/ld+json"
      dangerouslySetInnerHTML={{ __html: renderSecureJsonLd(structuredData) }}
    />
  );
};

// ✅ Additional components implemented:
// - OrganizationStructuredData
// - ProductListStructuredData
// - WebSiteStructuredData
// - SearchResultsStructuredData
// - BreadcrumbStructuredData
```

#### Breadcrumb Structured Data Usage

```typescript
// Example product detail page
<BreadcrumbStructuredData
  items={[
    { name: 'Home', url: '/' },
    { name: product.category.name, url: `/categories/${product.category.slug}` },
    { name: product.name, url: `/products/${product.slug}` },
  ]}
  />
```

The component accepts an ordered list of `{ name, url }` pairs. Each link is
constructed from the related slugs – category URLs use `category.slug` and
product URLs use `product.slug` – ensuring breadcrumbs mirror the site's
hierarchy for search engines.

### Sitemap Generation ✅ COMPREHENSIVE

```typescript
// src/app/sitemap.xml/route.ts - Sitemap Index
import { MetadataRoute } from 'next'
import { getProducts, getBrands, getRetailers } from '@/lib/data'
import { createServerSupabaseReadOnlyClient } from '@/lib/supabase/server'

export default async function sitemap(): Promise<MetadataRoute.Sitemap> {
  const supabase = createServerSupabaseReadOnlyClient()
  const baseUrl = process.env.NEXT_PUBLIC_SITE_URL || 'https://cashback-deals.com'

  // Get total counts to generate paginated sitemaps
  const productsCount = (await getProducts(supabase, {}, 1, 1)).pagination.total
  const brandsCount = (await getBrands(supabase, 1, 1)).pagination.total
  const retailersCount = (await getRetailers(supabase, {}, 1, 1)).pagination.total

  const sitemaps = [
    `${baseUrl}/sitemaps/static.xml`,
  ];

  // Add paginated sitemaps
  for (let i = 0; i < Math.ceil(productsCount / 5000); i++) {
    sitemaps.push(`${baseUrl}/sitemaps/products/${i + 1}.xml`);
  }
  // ... similar loops for brands and retailers

  return sitemaps.map(url => ({ url, lastModified: new Date() }));
}
```

## Performance Optimization Techniques ✅ IMPLEMENTED

### Code Splitting & Lazy Loading

The application uses Next.js 13+ automatic code splitting with additional optimizations:

```typescript
// next.config.js - ACTUAL 2025 OPTIMIZATIONS
module.exports = {
    // ✅ Package import optimization (2025 feature)
    experimental: {
        optimizeCss: true,
        optimizePackageImports: ['lucide-react', 'framer-motion'],
    },

    // ✅ Webpack optimizations for production
    webpack: (config, { dev, isServer }) => {
        if (!dev && !isServer) {
            config.optimization.splitChunks = {
                chunks: 'all',
                cacheGroups: {
                    vendor: {
                        test: /[\\/]node_modules[\\/]/,
                        name: 'vendors',
                        chunks: 'all',
                        priority: 10,
                    },
                },
            };
        }
        return config;
    },

    // ✅ Remove console logs in production
    compiler: {
        removeConsole: process.env.NODE_ENV === 'production',
    },
}
```

## Security Headers ✅ COMPREHENSIVE

```javascript
// next.config.js - ACTUAL SECURITY IMPLEMENTATION
const getSecurityHeaders = () => {
  const cspDirectives = {
    'default-src': ["'self'"],
    'script-src': [
      "'self'",
      "https://challenges.cloudflare.com", // ✅ Cloudflare Turnstile
      ...(isDevelopment ? ["'unsafe-eval'", "'unsafe-inline'"] : [])
    ],
    'img-src': [
      "'self'",
      "data:",
      "https://*.supabase.co",
      "https://*.amazonaws.com",
      "https://*.cloudfront.net",
      "https://images.samsung.com",
    ],
    'connect-src': [
      "'self'",
      "https://*.supabase.co",
      "https://*.ingest.de.sentry.io", // ✅ Sentry monitoring
    ],
    'frame-src': ["https://challenges.cloudflare.com"], // ✅ CAPTCHA support
    'worker-src': ["'self'", "blob:"], // ✅ Sentry replay workers
  };

  return [
    { key: 'Content-Security-Policy', value: cspString },
    { key: 'X-Content-Type-Options', value: 'nosniff' },
    { key: 'Referrer-Policy', value: 'strict-origin-when-cross-origin' },
    // ✅ HSTS in production
    ...(isProduction ? [{ key: 'Strict-Transport-Security', value: 'max-age=63072000; includeSubDomains; preload' }] : []),
  ];
};
```

## Missing Implementations ❌ GAPS IDENTIFIED

### 1. Real User Monitoring (RUM) - NOT IMPLEMENTED

```typescript
// ❌ src/lib/performance/rum.ts - DOES NOT EXIST
// This needs to be implemented for production monitoring
```

### 2. Performance Budget Monitoring - LIMITED

```typescript
// ⚠️ Only mock implementation in WebVitals component
export function checkPerformanceBudget(): Promise<{passed: boolean; metrics: Record<string, any>}> {
    // Currently returns mock data - needs real implementation
}
```

### 3. INP (Interaction to Next Paint) Monitoring - MISSING

```typescript
// ❌ WebVitals component missing INP tracking for 2025 compliance
// Need to add: onINP(handleMetric) from web-vitals library
```

## Next Steps & 2025 Recommendations

### High Priority (Immediate)
- [ ] Enable WebVitals monitoring in production environment
- [ ] Add INP (Interaction to Next Paint) tracking for 2025 Core Web Vitals
- [ ] Implement Real User Monitoring (RUM) system
- [ ] Create performance budget monitoring and alerting

### Medium Priority (Q1 2025)
- [ ] Implement hover-based prefetching optimization
- [ ] Add Service Worker for offline functionality
- [ ] Enhanced image optimization with blur placeholders
- [ ] Performance regression detection system

### Advanced Features (Q2 2025)
- [ ] GEO (Generative Engine Optimization) for AI search
- [ ] Advanced bundle splitting with Turbopack
- [ ] Edge computing optimizations
- [ ] A/B testing for performance optimizations

### Infrastructure Improvements
- [ ] Redis caching layer for database queries
- [ ] CDN optimization beyond Cloudflare
- [ ] Database query optimization and indexing
- [ ] API response compression optimization

## Current Status Summary

| Component | Implementation Status | Notes |
|-----------|---------------------|-------|
| **Core Web Vitals Monitoring** | ⚠️ Partial | Disabled in dev, missing INP |
| **Image Optimization** | ✅ Excellent | More advanced than documented |
| **Structured Data** | ✅ Excellent | Very comprehensive implementation |
| **Caching System** | ✅ Good | Well implemented with Next.js cache |
| **Security Headers** | ✅ Excellent | Comprehensive CSP and security |
| **Sitemap Generation** | ✅ Good | Dynamic with proper priorities |
| **Metadata Management** | ✅ Good | Functional but could be enhanced |
| **RUM Monitoring** | ❌ Missing | Critical gap for production |
| **Performance Budgets** | ❌ Limited | Mock implementation only |

## Architecture Corrections Made

1. **Infrastructure**: Corrected from Vercel to AWS Amplify + Cloudflare
2. **Site Name**: Corrected from "Cashback Deals" to "RebateRay"
3. **Core Web Vitals**: Updated FID → INP for 2025 compliance
4. **Component Status**: Accurate reflection of actual implementations
5. **Missing Features**: Honest assessment of gaps to be addressed

This V2 documentation provides an accurate assessment of the current state and clear roadmap for improvements.