# SEO Validation System

This document describes the comprehensive SEO validation and linting system implemented for the RebateRay project.

## Overview

The SEO validation system provides automated testing and validation of SEO implementation across all pages to ensure consistency, prevent regressions, and maintain high SEO standards.

## Components

### 1. SEO Validator Utility (`src/lib/seo-validator.ts`)

The core validation logic that can programmatically validate SEO implementation:

```typescript
import { validatePageSEO, generateSEOReport, SEO_VALIDATION_PRESETS } from '@/lib/seo-validator';

const result = validatePageSEO(metadata, jsonLdScripts, expectedSchemaTypes);
console.log(generateSEOReport(result));
```

**Features:**
- Validates metadata completeness and quality
- Checks structured data (JSON-LD) validity
- Ensures URL consistency (canonical vs OpenGraph)
- Validates absolute URLs
- Provides scoring and grading system
- Generates detailed reports

### 2. Unit Tests (`tests/seo/seo-validation.test.ts`)

Comprehensive unit tests for SEO utilities:

```bash
npm run test:seo
```

**Validates:**
- Unified SEO utility functions
- Metadata generation consistency
- URL construction patterns
- Error handling behavior
- Configuration objects

### 3. E2E Tests (`tests/e2e/seo-compliance.spec.ts`)

Browser-based testing using Playwright:

```bash
npm run test:seo:e2e
```

**Tests:**
- Live page SEO implementation
- Cross-page consistency
- Schema.org structured data
- Performance impact
- Accessibility considerations

### 4. SEO Linting Script (`scripts/seo-lint.ts`)

Development tool for manual SEO validation:

```bash
# Test all configured pages
npm run seo:lint

# Test specific page
npm run seo:lint:custom /brands/samsung-uk brand

# Test custom URL with type
tsx scripts/seo-lint.ts /products/custom-product product
```

## SEO Validation Rules

### Metadata Requirements

| Rule | Severity | Description |
|------|----------|-------------|
| Title required | Error | Page must have a title |
| Title length 30-60 chars | Warning | Optimal length for search results |
| Description required | Error | Meta description must exist |
| Description length 120-160 chars | Warning | Optimal length for search snippets |
| Canonical URL required | Error (strict mode) | Self-referencing canonical required |
| Canonical URL absolute | Error | Must use full URL with protocol |
| OpenGraph consistency | Error | OG URL must match canonical URL |

### Structured Data Requirements

| Rule | Severity | Description |
|------|----------|-------------|
| JSON-LD present | Error | At least one structured data block |
| Valid JSON syntax | Error | All JSON-LD must be parseable |
| Schema.org context | Warning | Should use https://schema.org |
| Required @type | Error | Each block needs a type |
| Absolute URLs | Error | All URLs in structured data must be absolute |
| Expected schema types | Warning | Page should have appropriate schemas |

### URL Consistency Requirements

| Rule | Severity | Description |
|------|----------|-------------|
| Canonical = OpenGraph | Error | URLs must be identical |
| Absolute URLs only | Error | No relative URLs allowed |
| Domain consistency | Warning | Should use configured site URL |
| Structured data URLs | Error | All schema URLs must be absolute |

## Page Type Presets

### Brand Pages
```typescript
{
  expectedSchemaTypes: ['Brand', 'BreadcrumbList'],
  strictMode: true,
  checkAbsoluteUrls: true
}
```

### Product Pages
```typescript
{
  expectedSchemaTypes: ['Product', 'BreadcrumbList'],
  strictMode: true,
  checkAbsoluteUrls: true
}
```

### Retailer Pages
```typescript
{
  expectedSchemaTypes: ['Organization', 'BreadcrumbList'],
  strictMode: true,
  checkAbsoluteUrls: true
}
```

### Listing Pages
```typescript
{
  expectedSchemaTypes: ['CollectionPage', 'BreadcrumbList'],
  strictMode: false,
  checkAbsoluteUrls: true
}
```

## Integration with Development Workflow

### Pre-commit Hooks

Add to `.husky/pre-commit`:

```bash
#!/bin/sh
. "$(dirname "$0")/_/husky.sh"

# Run SEO linting
npm run seo:lint
```

### CI/CD Pipeline

Add to GitHub Actions workflow:

```yaml
- name: Run SEO Tests
  run: |
    npm run test:seo
    npm run build
    npm run start &
    sleep 10
    npm run seo:lint
    npm run test:seo:e2e
```

### Development Scripts

| Command | Purpose |
|---------|---------|
| `npm run test:seo` | Run unit tests for SEO utilities |
| `npm run test:seo:e2e` | Run E2E SEO compliance tests |
| `npm run seo:lint` | Validate all configured pages |
| `npm run seo:lint:custom /path type` | Validate specific page |

## Scoring System

The validation system uses a 100-point scoring system:

- **90-100**: A+ (Excellent) - Production ready
- **80-89**: A (Good) - Minor improvements needed
- **70-79**: B (Fair) - Several issues to address
- **60-69**: C (Poor) - Major problems present
- **0-59**: F (Failing) - Critical issues, blocks deployment

### Score Breakdown

- **Metadata (60%)**:
  - Title: 15 points
  - Description: 15 points
  - Canonical URL: 10 points
  - OpenGraph: 8 points
  - Twitter Cards: 4 points
  - Other warnings: 2-5 points each

- **Structured Data (40%)**:
  - JSON-LD presence: 20 points
  - Valid JSON: 10 points
  - Required properties: 5 points each
  - URL validation: 3 points each
  - Schema consistency: 2 points each

## Troubleshooting

### Common Issues

1. **"Canonical URL must be absolute"**
   - Ensure `SITE_URL` is configured correctly
   - Check that URLs start with `http://` or `https://`

2. **"OpenGraph URL and canonical URL must be identical"**
   - Both should use the same absolute URL
   - Check unified SEO utility implementation

3. **"Invalid JSON-LD"**
   - Validate JSON syntax
   - Ensure all required properties are present
   - Check for null/undefined values

4. **"Missing expected schema types"**
   - Verify correct schema is being generated
   - Check page type configuration
   - Ensure structured data components are included

### Debug Mode

Enable detailed logging:

```typescript
// In seo-validator.ts
const DEBUG = process.env.NODE_ENV === 'development';
if (DEBUG) console.log('Validation details:', result);
```

## Future Enhancements

1. **Integration with Google Search Console**
   - Automatic structured data validation
   - Index coverage reporting
   - Search performance tracking

2. **Visual SEO Testing**
   - SERP preview generation
   - Social media preview validation
   - Mobile-friendly testing

3. **Performance Integration**
   - Core Web Vitals impact assessment
   - Resource loading optimization
   - Critical rendering path analysis

4. **AI-Powered Optimization**
   - Automatic title/description optimization
   - Schema markup suggestions
   - Competitor analysis integration

## Contributing

When adding new pages or modifying SEO implementation:

1. Run `npm run test:seo` to ensure unit tests pass
2. Add page to `scripts/seo-lint.ts` configuration
3. Run `npm run seo:lint` to validate implementation
4. Add E2E test case if creating new page type
5. Update this documentation

## Support

For questions or issues with the SEO validation system:

1. Check the troubleshooting section above
2. Review the validation error messages and warnings
3. Examine the test files for examples
4. Consult the unified SEO utilities (`src/lib/seo-utils.ts`)

The SEO validation system ensures consistent, high-quality SEO implementation across all pages while preventing regressions and maintaining search engine optimization standards.