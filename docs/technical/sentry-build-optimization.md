# Sentry Build Optimization

This document explains how to control Sentry integration during builds to optimize build performance.

## Overview

Sentry integration can slow down Next.js builds due to source map uploading and telemetry collection. This configuration allows you to disable Sentry during builds while keeping it enabled at runtime when needed.

## Configuration

The Sentry build integration is controlled by the `ENABLE_SENTRY` environment variable:

- When `ENABLE_SENTRY=true`: Sentry is enabled during builds (source maps uploaded, telemetry collected)
- When `ENABLE_SENTRY=false`: Sentry is disabled during builds (no source map upload, no telemetry)
- In production builds: Sentry is always enabled regardless of `ENABLE_SENTRY` setting

## Environment Variables

### ENABLE_SENTRY
Controls whether Sentry is enabled during builds:
```
ENABLE_SENTRY=true   # Enable Sentry during builds (slower builds)
ENABLE_SENTRY=false  # Disable Sentry during builds (faster builds)
```

### ENABLE_SENTRY_LOCAL
Controls whether Sentry is enabled in local development:
```
ENABLE_SENTRY_LOCAL=true   # Enable Sentry in local development
ENABLE_SENTRY_LOCAL=false  # Disable Sentry in local development (default)
```

## Usage Examples

### Speed Up Development Builds
To speed up development builds, ensure your `.env.local` file contains:
```
ENABLE_SENTRY=false
ENABLE_SENTRY_LOCAL=false
```

### Enable Sentry for Production Builds Only
To enable Sentry only for production builds:
```
# In .env.local
ENABLE_SENTRY=false
ENABLE_SENTRY_LOCAL=false

# In .env.production
ENABLE_SENTRY=true
```

### Test Builds with Sentry Disabled
To test builds with Sentry completely disabled:
```
ENABLE_SENTRY=false NODE_ENV=production npm run build
```

## Build Performance Impact

With Sentry disabled during builds:
- Source map uploading is skipped
- Telemetry collection is disabled
- Build times can be significantly faster (30-60 seconds improvement)
- Sentry runtime functionality remains unaffected when enabled via application code

## Troubleshooting

If you need to verify Sentry build status, check the console output during builds for:
```
[Sentry] Build-time Sentry integration disabled - skipping source map upload and telemetry
```
or
```
[Sentry] Build-time Sentry integration enabled
```

## Related Files

- `next.config.js` - Conditional Sentry build configuration
- `src/instrumentation.ts` - Runtime Sentry initialization control
- `src/sentry.server.config.ts` - Server-side Sentry configuration
- `src/sentry.edge.config.ts` - Edge-side Sentry configuration