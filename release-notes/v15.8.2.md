## [12 Aug 2025 16:49] - v15.8.2 - 🔎 SEO & AI Policy Enhancements || Branch: feature/buildopt

### Summary
Adds self-referencing canonicals and JSON‑LD to Brands pages, implements agent-specific AI bot policy (robots.txt + ai.txt), and introduces Playwright tests to validate SEO/AI endpoints.

### Components Modified
- ENHANCEMENT: src/app/brands/page.tsx — added canonical, BreadcrumbList JSON‑LD via component
- ENHANCEMENT: src/app/brands/[id]/page.tsx — added canonical, Brand + BreadcrumbList JSON‑LD via components
- FEATURE: src/app/ai.txt/route.ts — new endpoint to serve AI bot policy
- ENHANCEMENT: src/app/robots.ts — agent-specific rules (Allow: Googlebot, Google‑Extended; Disallow: GPTBot, CCBot, PerplexityBot, <PERSON>‑<PERSON>, Applebot‑Extended, Bytespider)
- TESTS: tests/e2e/seo/brands-seo.spec.ts — validates brand pages canonicals and JSON‑LD
- TESTS: tests/e2e/seo/robots-ai.spec.ts — validates robots.txt policy and ai.txt
- DOCS: docs/performance/PERFORMANCE_SEO_V2.md — updated with v15.8.2 changes
- DOCS: docs/development/TESTING.md — added note on new SEO/AI tests

### Documentation Updates
- docs/performance/PERFORMANCE_SEO_V2.md: Added v15.8.2 update note and summary of brand SEO + AI policy changes.
- docs/development/TESTING.md: Added v15.8.2 note and test locations for the new SEO/AI E2E tests.
- docs/performance/NEW_augmentcode_analysis.md: Replaced with expanded VoucherBot SEO Auditor report.

### Impact
- ✅ Search engines receive accurate canonicals for brand pages and richer structured data for better indexing/snippets.
- ✅ AI policy aligned with Balanced Approach: Google-Extended allowed; third-party training bots disallowed.
- ✅ Automated validation with Playwright increases confidence and prevents regressions.
- ⚠️ Ensure NEXT_PUBLIC_SITE_URL is set per environment (localhost:3000 locally) to avoid canonical/sitemap host mismatches.

### Technical Notes
- Canonicals are generated with absolute URLs using SITE_URL from src/config/domains.ts.
- JSON‑LD is rendered via script tags using StructuredData components (avoids issues with metadata.other).
- robots.txt now uses array-based rules to support agent-specific policies.
- /ai.txt is served via a Next.js route handler returning plaintext.

### Files Changed
- src/app/brands/page.tsx
- src/app/brands/[id]/page.tsx
- src/app/robots.ts
- src/app/ai.txt/route.ts (new)
- tests/e2e/seo/brands-seo.spec.ts (new)
- tests/e2e/seo/robots-ai.spec.ts (new)
- docs/performance/PERFORMANCE_SEO_V2.md (updated)
- docs/development/TESTING.md (updated)
- docs/performance/NEW_augmentcode_analysis.md (replaced)

### Future Considerations & Monitoring
- Consider MerchantPromotion schema for promotions on brand pages; add validFrom/validThrough and couponCode when applicable.
- Decide canonical strategy for faceted products pages (e.g., promotion filters); add noindex where appropriate.
- Add CI gate to run SEO/AI Playwright tests on PRs touching SEO-related files.

