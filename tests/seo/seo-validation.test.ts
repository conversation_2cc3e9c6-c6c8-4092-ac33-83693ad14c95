/**
 * SEO Validation Tests
 * Ensures consistent SEO patterns across all pages and prevents regressions
 */

import { generateBrandSEO, generateProductSEO, generateRetailerSEO, withSEOErrorHandling } from '@/lib/seo-utils';
import { SITE_URL } from '@/config/domains';

describe('SEO Validation Suite', () => {
  describe('Unified SEO Utilities', () => {
    const mockBrand = {
      id: 'test-brand-id',
      name: 'Test Brand',
      slug: 'test-brand',
      description: 'Test brand description',
      logoUrl: 'https://example.com/logo.png',
    };

    const mockProduct = {
      id: 'test-product-id',
      name: 'Test Product',
      slug: 'test-product',
      description: 'Test product description',
      images: ['https://example.com/product.jpg'],
      brand: { name: 'Test Brand', logoUrl: 'https://example.com/brand-logo.png' },
      category: { name: 'Electronics', slug: 'electronics' },
      modelNumber: 'TP-001',
      retailerOffers: [],
    };

    const mockRetailer = {
      id: 'test-retailer-id',
      name: 'Test Retailer',
      slug: 'test-retailer',
      description: 'Test retailer description',
      logoUrl: 'https://example.com/retailer-logo.png',
    };

    describe('generateBrandSEO', () => {
      it('should generate consistent metadata structure', () => {
        const result = generateBrandSEO(mockBrand);
        
        expect(result).toHaveProperty('metadata');
        expect(result).toHaveProperty('structuredData');
        expect(result).toHaveProperty('breadcrumbItems');
        expect(result.metadata).toHaveProperty('title');
        expect(result.metadata).toHaveProperty('description');
        expect(result.metadata).toHaveProperty('alternates');
        expect(result.metadata).toHaveProperty('openGraph');
      });

      it('should generate absolute canonical URLs', () => {
        const result = generateBrandSEO(mockBrand);
        
        expect(result.metadata.alternates?.canonical).toMatch(/^https?:\/\//);
        expect(result.metadata.alternates?.canonical).toContain(SITE_URL);
      });

      it('should have consistent OpenGraph and canonical URLs', () => {
        const result = generateBrandSEO(mockBrand);
        
        const canonicalUrl = result.metadata.alternates?.canonical;
        const ogUrl = result.metadata.openGraph?.url;
        
        expect(canonicalUrl).toEqual(ogUrl);
      });

      it('should generate proper breadcrumb structure', () => {
        const result = generateBrandSEO(mockBrand);
        
        expect(result.breadcrumbItems).toHaveLength(3);
        expect(result.breadcrumbItems[0]).toEqual({ name: 'Home', url: '/' });
        expect(result.breadcrumbItems[1]).toEqual({ name: 'Brands', url: '/brands' });
        expect(result.breadcrumbItems[2].name).toBe(mockBrand.name);
      });
    });

    describe('generateProductSEO', () => {
      it('should generate consistent metadata structure', () => {
        const result = generateProductSEO(mockProduct);
        
        expect(result).toHaveProperty('metadata');
        expect(result).toHaveProperty('structuredData');
        expect(result).toHaveProperty('breadcrumbItems');
        expect(result.metadata).toHaveProperty('title');
        expect(result.metadata).toHaveProperty('description');
        expect(result.metadata).toHaveProperty('alternates');
        expect(result.metadata).toHaveProperty('openGraph');
      });

      it('should generate absolute canonical URLs', () => {
        const result = generateProductSEO(mockProduct);
        
        expect(result.metadata.alternates?.canonical).toMatch(/^https?:\/\//);
        expect(result.metadata.alternates?.canonical).toContain(SITE_URL);
      });

      it('should have consistent OpenGraph and canonical URLs', () => {
        const result = generateProductSEO(mockProduct);
        
        const canonicalUrl = result.metadata.alternates?.canonical;
        const ogUrl = result.metadata.openGraph?.url;
        
        expect(canonicalUrl).toEqual(ogUrl);
      });

      it('should include brand in title', () => {
        const result = generateProductSEO(mockProduct);
        
        expect(result.metadata.title).toContain(mockProduct.name);
        expect(result.metadata.title).toContain('Best Cashback Deals');
      });
    });

    describe('generateRetailerSEO', () => {
      it('should generate consistent metadata structure', () => {
        const result = generateRetailerSEO(mockRetailer);
        
        expect(result).toHaveProperty('metadata');
        expect(result).toHaveProperty('structuredData');
        expect(result).toHaveProperty('breadcrumbItems');
      });

      it('should generate absolute canonical URLs', () => {
        const result = generateRetailerSEO(mockRetailer);
        
        expect(result.metadata.alternates?.canonical).toMatch(/^https?:\/\//);
        expect(result.metadata.alternates?.canonical).toContain(SITE_URL);
      });
    });
  });

  describe('Error Handling', () => {
    it('should handle errors gracefully with withSEOErrorHandling', async () => {
      const errorFunction = async () => {
        throw new Error('Test error');
      };

      const { data, errorMetadata } = await withSEOErrorHandling(errorFunction, 'brand');
      
      expect(data).toBeNull();
      expect(errorMetadata).toBeDefined();
      expect(errorMetadata?.title).toContain('Not Found');
    });

    it('should return data when function succeeds', async () => {
      const successFunction = async () => {
        return { success: true };
      };

      const { data, errorMetadata } = await withSEOErrorHandling(successFunction, 'brand');
      
      expect(data).toEqual({ success: true });
      expect(errorMetadata).toBeNull();
    });
  });

  describe('SEO Pattern Consistency', () => {
    it('should ensure all SEO functions return the same structure', () => {
      const brandResult = generateBrandSEO(mockBrand);
      const productResult = generateProductSEO(mockProduct);
      const retailerResult = generateRetailerSEO(mockRetailer);

      // Check all results have the same top-level structure
      const expectedKeys = ['metadata', 'structuredData', 'breadcrumbItems'];
      
      expectedKeys.forEach(key => {
        expect(brandResult).toHaveProperty(key);
        expect(productResult).toHaveProperty(key);
        expect(retailerResult).toHaveProperty(key);
      });

      // Check metadata structure consistency
      const metadataKeys = ['title', 'description', 'alternates', 'openGraph'];
      
      metadataKeys.forEach(key => {
        expect(brandResult.metadata).toHaveProperty(key);
        expect(productResult.metadata).toHaveProperty(key);
        expect(retailerResult.metadata).toHaveProperty(key);
      });
    });

    it('should ensure all URLs are absolute', () => {
      const results = [
        generateBrandSEO(mockBrand),
        generateProductSEO(mockProduct),
        generateRetailerSEO(mockRetailer)
      ];

      results.forEach(result => {
        const canonicalUrl = result.metadata.alternates?.canonical;
        const ogUrl = result.metadata.openGraph?.url;
        
        expect(canonicalUrl).toMatch(/^https?:\/\//);
        expect(ogUrl).toMatch(/^https?:\/\//);
        
        // Ensure they're identical
        expect(canonicalUrl).toEqual(ogUrl);
      });
    });

    it('should ensure breadcrumbs always start with Home', () => {
      const results = [
        generateBrandSEO(mockBrand),
        generateProductSEO(mockProduct),
        generateRetailerSEO(mockRetailer)
      ];

      results.forEach(result => {
        expect(result.breadcrumbItems).toHaveLength.greaterThan(0);
        expect(result.breadcrumbItems[0]).toEqual({ name: 'Home', url: '/' });
      });
    });
  });
});

describe('SEO Configuration Validation', () => {
  describe('Keywords Templates', () => {
    it('should return arrays of strings only', () => {
      const { keywordTemplates } = require('@/lib/seo-config');
      
      const brandKeywords = keywordTemplates.brand('Test Brand');
      const productKeywords = keywordTemplates.product('Test Product', 'Test Brand');
      const retailerKeywords = keywordTemplates.retailer('Test Retailer');
      const listingKeywords = keywordTemplates.listing('products');

      [brandKeywords, productKeywords, retailerKeywords, listingKeywords].forEach(keywords => {
        expect(Array.isArray(keywords)).toBe(true);
        keywords.forEach(keyword => {
          expect(typeof keyword).toBe('string');
          expect(keyword.length).toBeGreaterThan(0);
        });
      });
    });
  });

  describe('Structured Data Templates', () => {
    it('should generate valid JSON-LD', () => {
      const { structuredDataTemplates } = require('@/lib/seo-config');
      
      const brandData = structuredDataTemplates.brand({
        id: 'test-id',
        name: 'Test Brand',
        description: 'Test description',
        logoUrl: 'https://example.com/logo.png',
        canonicalUrl: 'https://example.com/brand'
      });

      expect(brandData).toHaveProperty('@context', 'https://schema.org');
      expect(brandData).toHaveProperty('@type', 'Brand');
      expect(brandData).toHaveProperty('name');
      expect(brandData).toHaveProperty('description');
    });
  });
});