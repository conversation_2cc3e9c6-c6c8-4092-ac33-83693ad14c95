
import axios from 'axios';
import { SITE_URL } from '@/config/domains';

test('sitemap uses SITE_URL', async () => {
  const xml = await axios.get(`${SITE_URL}/sitemap.xml`);
  expect(xml.data).not.toMatch('localhost:3000');
});

describe('Sitemap Content Validation', () => {
  test('main sitemap.xml should not be empty', async () => {
    const response = await axios.get(`${SITE_URL}/sitemap.xml`);
    expect(response.status).toBe(200);
    expect(response.headers['content-type']).toMatch('application/xml');
    
    // Should contain <sitemapindex> with at least some sitemap entries
    expect(response.data).toMatch(/<sitemapindex/);
    expect(response.data).toMatch(/<sitemap>/);
    expect(response.data).toMatch(/<loc>/);
  });

  test('retailers sitemap should contain valid URLs or fail if empty', async () => {
    const response = await axios.get(`${SITE_URL}/sitemaps/retailers/1`);
    expect(response.status).toBe(200);
    expect(response.headers['content-type']).toMatch('application/xml');
    
    // Parse XML to check for URLs
    expect(response.data).toMatch(/<urlset/);
    
    // CRITICAL: Must have real URLs, not test/fallback data
    const hasUrls = response.data.includes('<url>');
    const hasTestData = response.data.includes('amazon-uk') || response.data.includes('john-lewis');
    
    if (hasUrls) {
      // If we have URLs, they should be real data (not test fallbacks)
      expect(hasTestData).toBe(false);
      
      // Should contain valid retailer URLs with proper lastmod
      expect(response.data).toMatch(/<loc>[^<]+\/retailers\/[^<]+<\/loc>/);
      expect(response.data).toMatch(/<lastmod>\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}/);
    } else {
      // If no URLs, the sitemap should fail rather than show fallback data
      fail('Retailers sitemap is empty - this indicates missing data that should be investigated');
    }
  });

  test('products sitemap should contain valid URLs or fail if empty', async () => {
    const response = await axios.get(`${SITE_URL}/sitemaps/products/1`);
    expect(response.status).toBe(200);
    expect(response.headers['content-type']).toMatch('application/xml');
    
    expect(response.data).toMatch(/<urlset/);
    
    const hasUrls = response.data.includes('<url>');
    if (hasUrls) {
      expect(response.data).toMatch(/<loc>[^<]+\/products\/[^<]+<\/loc>/);
      expect(response.data).toMatch(/<lastmod>\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}/);
    } else {
      fail('Products sitemap is empty - this indicates missing data that should be investigated');
    }
  });

  test('brands sitemap should contain valid URLs or fail if empty', async () => {
    const response = await axios.get(`${SITE_URL}/sitemaps/brands/1`);
    expect(response.status).toBe(200);
    expect(response.headers['content-type']).toMatch('application/xml');
    
    expect(response.data).toMatch(/<urlset/);
    
    const hasUrls = response.data.includes('<url>');
    if (hasUrls) {
      expect(response.data).toMatch(/<loc>[^<]+\/brands\/[^<]+<\/loc>/);
      expect(response.data).toMatch(/<lastmod>\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}/);
    } else {
      fail('Brands sitemap is empty - this indicates missing data that should be investigated');
    }
  });

  test('static sitemap should always contain core pages', async () => {
    const response = await axios.get(`${SITE_URL}/sitemaps/static`);
    expect(response.status).toBe(200);
    expect(response.headers['content-type']).toMatch('application/xml');
    
    expect(response.data).toMatch(/<urlset/);
    expect(response.data).toMatch(/<url>/);
    
    // Should contain homepage and core pages
    expect(response.data).toMatch(new RegExp(`<loc>${SITE_URL}</loc>`));
    expect(response.data).toMatch(new RegExp(`<loc>${SITE_URL}/products</loc>`));
    expect(response.data).toMatch(new RegExp(`<loc>${SITE_URL}/brands</loc>`));
    expect(response.data).toMatch(new RegExp(`<loc>${SITE_URL}/retailers</loc>`));
  });
});
