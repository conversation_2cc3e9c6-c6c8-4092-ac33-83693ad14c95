/**
 * E2E SEO Compliance Tests
 * Validates SEO implementation across all page types in a real browser environment
 */

import { test, expect, Page } from '@playwright/test';

interface SEOTestResult {
  jsonLdCount: number;
  canonicalUrl: string | null;
  ogUrl: string | null;
  pageTitle: string;
  metaDescription: string | null;
  jsonLdTypes: Array<{
    index: number;
    type: string;
    context: string;
    name?: string;
    url?: string;
  }>;
  hasErrors: boolean;
}

async function extractSEOData(page: Page): Promise<SEOTestResult> {
  return await page.evaluate(() => {
    const results: SEOTestResult = {
      jsonLdCount: document.querySelectorAll('script[type="application/ld+json"]').length,
      canonicalUrl: document.querySelector('link[rel="canonical"]')?.getAttribute('href') || null,
      ogUrl: document.querySelector('meta[property="og:url"]')?.getAttribute('content') || null,
      pageTitle: document.title,
      metaDescription: document.querySelector('meta[name="description"]')?.getAttribute('content') || null,
      jsonLdTypes: [],
      hasErrors: false
    };

    // Parse JSON-LD content
    document.querySelectorAll('script[type="application/ld+json"]').forEach((script, index) => {
      try {
        const content = JSON.parse(script.textContent || '{}');
        results.jsonLdTypes.push({
          index: index,
          type: content['@type'],
          context: content['@context'],
          name: content.name,
          url: content.url || content.item?.url || 'N/A'
        });
      } catch (e) {
        results.jsonLdTypes.push({
          index: index,
          type: 'ERROR',
          context: 'ERROR',
          name: 'Failed to parse JSON-LD'
        });
        results.hasErrors = true;
      }
    });

    return results;
  });
}

test.describe('SEO Compliance Tests', () => {
  test.describe('Brand Pages', () => {
    test('Samsung UK brand page should have proper SEO implementation', async ({ page }) => {
      await page.goto('/brands/samsung-uk');
      const seoData = await extractSEOData(page);

      // Basic structure validation
      expect(seoData.jsonLdCount).toBe(2);
      expect(seoData.hasErrors).toBe(false);
      
      // URL consistency validation
      expect(seoData.canonicalUrl).toBeTruthy();
      expect(seoData.ogUrl).toBeTruthy();
      expect(seoData.canonicalUrl).toBe(seoData.ogUrl);
      
      // Absolute URLs validation
      expect(seoData.canonicalUrl).toMatch(/^https?:\/\//);
      expect(seoData.ogUrl).toMatch(/^https?:\/\//);
      
      // Title and description validation
      expect(seoData.pageTitle).toBeTruthy();
      expect(seoData.pageTitle).toContain('Samsung UK');
      expect(seoData.pageTitle).toContain('Promotions & Cashback Deals');
      expect(seoData.metaDescription).toBeTruthy();
      expect(seoData.metaDescription!.length).toBeGreaterThan(120);
      
      // Schema validation
      const schemaTypes = seoData.jsonLdTypes.map(item => item.type);
      expect(schemaTypes).toContain('Brand');
      expect(schemaTypes).toContain('BreadcrumbList');
      
      // Brand schema URL validation
      const brandSchema = seoData.jsonLdTypes.find(item => item.type === 'Brand');
      expect(brandSchema?.url).toMatch(/^https?:\/\//);
      expect(brandSchema?.name).toBe('Samsung UK');
    });

    test('Brand pages should have consistent breadcrumb structure', async ({ page }) => {
      await page.goto('/brands/samsung-uk');
      const seoData = await extractSEOData(page);
      
      const breadcrumbSchema = seoData.jsonLdTypes.find(item => item.type === 'BreadcrumbList');
      expect(breadcrumbSchema).toBeTruthy();
      
      // Validate breadcrumb structure in the actual JSON-LD
      const breadcrumbContent = await page.evaluate(() => {
        const breadcrumbScript = Array.from(document.querySelectorAll('script[type="application/ld+json"]'))
          .find(script => {
            const content = JSON.parse(script.textContent || '{}');
            return content['@type'] === 'BreadcrumbList';
          });
        
        return breadcrumbScript ? JSON.parse(breadcrumbScript.textContent || '{}') : null;
      });
      
      expect(breadcrumbContent).toBeTruthy();
      expect(breadcrumbContent.itemListElement).toHaveLength(3);
      expect(breadcrumbContent.itemListElement[0].name).toBe('Home');
      expect(breadcrumbContent.itemListElement[1].name).toBe('Brands');
      expect(breadcrumbContent.itemListElement[2].name).toBe('Samsung UK');
    });
  });

  test.describe('Product Pages', () => {
    test('Product page should have proper SEO implementation', async ({ page }) => {
      // Navigate to products page first
      await page.goto('/products');
      
      // Click on first product
      await page.click('[data-testid="product-card"]');
      
      const seoData = await extractSEOData(page);

      // Basic structure validation
      expect(seoData.jsonLdCount).toBe(2);
      expect(seoData.hasErrors).toBe(false);
      
      // URL consistency validation
      expect(seoData.canonicalUrl).toBeTruthy();
      expect(seoData.ogUrl).toBeTruthy();
      expect(seoData.canonicalUrl).toBe(seoData.ogUrl);
      
      // Absolute URLs validation
      expect(seoData.canonicalUrl).toMatch(/^https?:\/\//);
      expect(seoData.ogUrl).toMatch(/^https?:\/\//);
      
      // Title and description validation
      expect(seoData.pageTitle).toBeTruthy();
      expect(seoData.pageTitle).toContain('Best Cashback Deals');
      expect(seoData.metaDescription).toBeTruthy();
      
      // Schema validation
      const schemaTypes = seoData.jsonLdTypes.map(item => item.type);
      expect(schemaTypes).toContain('Product');
      expect(schemaTypes).toContain('BreadcrumbList');
      
      // Product schema URL validation
      const productSchema = seoData.jsonLdTypes.find(item => item.type === 'Product');
      expect(productSchema?.url).toMatch(/^https?:\/\//);
      expect(productSchema?.name).toBeTruthy();
    });

    test('Product pages should have proper brand association', async ({ page }) => {
      await page.goto('/products');
      await page.click('[data-testid="product-card"]');
      
      const productSchemaContent = await page.evaluate(() => {
        const productScript = Array.from(document.querySelectorAll('script[type="application/ld+json"]'))
          .find(script => {
            const content = JSON.parse(script.textContent || '{}');
            return content['@type'] === 'Product';
          });
        
        return productScript ? JSON.parse(productScript.textContent || '{}') : null;
      });
      
      expect(productSchemaContent).toBeTruthy();
      expect(productSchemaContent.brand).toBeTruthy();
      expect(productSchemaContent.brand.name).toBeTruthy();
    });
  });

  test.describe('Cross-Page Consistency', () => {
    test('All pages should use absolute URLs consistently', async ({ page }) => {
      const testPages = [
        '/brands/samsung-uk',
        '/products'
      ];

      for (const testPage of testPages) {
        await page.goto(testPage);
        
        if (testPage === '/products') {
          // For products listing, just check the listing page SEO
          const seoData = await extractSEOData(page);
          if (seoData.canonicalUrl) {
            expect(seoData.canonicalUrl).toMatch(/^https?:\/\//);
          }
          if (seoData.ogUrl) {
            expect(seoData.ogUrl).toMatch(/^https?:\/\//);
          }
        } else {
          const seoData = await extractSEOData(page);
          
          // All URLs should be absolute
          expect(seoData.canonicalUrl).toMatch(/^https?:\/\//);
          expect(seoData.ogUrl).toMatch(/^https?:\/\//);
          
          // Canonical and OG URLs should match
          expect(seoData.canonicalUrl).toBe(seoData.ogUrl);
          
          // All structured data URLs should be absolute
          seoData.jsonLdTypes.forEach(schema => {
            if (schema.url && schema.url !== 'N/A') {
              expect(schema.url).toMatch(/^https?:\/\//);
            }
          });
        }
      }
    });

    test('All pages should have required meta tags', async ({ page }) => {
      const testPages = [
        '/brands/samsung-uk'
      ];

      for (const testPage of testPages) {
        await page.goto(testPage);
        
        const metaTags = await page.evaluate(() => ({
          title: document.title,
          description: document.querySelector('meta[name="description"]')?.getAttribute('content'),
          canonical: document.querySelector('link[rel="canonical"]')?.getAttribute('href'),
          ogTitle: document.querySelector('meta[property="og:title"]')?.getAttribute('content'),
          ogDescription: document.querySelector('meta[property="og:description"]')?.getAttribute('content'),
          ogUrl: document.querySelector('meta[property="og:url"]')?.getAttribute('content'),
          ogType: document.querySelector('meta[property="og:type"]')?.getAttribute('content')
        }));
        
        // Required meta tags
        expect(metaTags.title).toBeTruthy();
        expect(metaTags.description).toBeTruthy();
        expect(metaTags.canonical).toBeTruthy();
        
        // OpenGraph tags
        expect(metaTags.ogTitle).toBeTruthy();
        expect(metaTags.ogDescription).toBeTruthy();
        expect(metaTags.ogUrl).toBeTruthy();
        expect(metaTags.ogType).toBe('website');
      }
    });
  });

  test.describe('Performance and Accessibility', () => {
    test('SEO-related elements should not impact Core Web Vitals', async ({ page }) => {
      await page.goto('/brands/samsung-uk');
      
      // Measure performance
      const performanceMetrics = await page.evaluate(() => {
        return {
          domContentLoaded: performance.timing.domContentLoadedEventEnd - performance.timing.navigationStart,
          loadComplete: performance.timing.loadEventEnd - performance.timing.navigationStart,
          jsonLdScriptCount: document.querySelectorAll('script[type="application/ld+json"]').length
        };
      });
      
      // SEO shouldn't significantly impact performance
      expect(performanceMetrics.jsonLdScriptCount).toBeLessThanOrEqual(3);
      expect(performanceMetrics.domContentLoaded).toBeLessThan(5000); // 5 seconds max
    });

    test('Structured data should be accessible to screen readers', async ({ page }) => {
      await page.goto('/brands/samsung-uk');
      
      // Check that JSON-LD scripts don't interfere with screen readers
      const jsonLdScripts = await page.locator('script[type="application/ld+json"]').count();
      expect(jsonLdScripts).toBeGreaterThan(0);
      
      // Ensure the scripts are not visible to screen readers (they shouldn't be)
      const visibleJsonLdElements = await page.locator('script[type="application/ld+json"]:visible').count();
      expect(visibleJsonLdElements).toBe(0);
    });
  });
});