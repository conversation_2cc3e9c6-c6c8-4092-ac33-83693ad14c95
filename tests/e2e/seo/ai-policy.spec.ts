import { test, expect } from '@playwright/test'

const BASE = 'http://localhost:3000'

/**
 * Validates AI bot policy across robots.txt and ai.txt
 */
test.describe('AI Bot Policy', () => {
  test('robots.txt contains agent-specific rules', async ({ page }) => {
    await page.goto(`${BASE}/robots.txt`)
    const text = await page.locator('body').innerText()

    expect(text).toContain('User-Agent: Googlebot')
    expect(text).toContain('Allow: /')
    expect(text).toContain('User-Agent: Google-Extended')
    expect(text).toContain('User-Agent: GPTBot')
    expect(text).toContain('Disallow: /')
    expect(text).toContain('User-Agent: CCBot')
    expect(text).toContain('User-Agent: PerplexityBot')
    expect(text).toContain('User-Agent: Claude-Web')
    expect(text).toContain('User-Agent: Applebot-Extended')
    expect(text).toContain('User-Agent: Bytespider')
  })

  test('ai.txt is served and reflects policy', async ({ page }) => {
    await page.goto(`${BASE}/ai.txt`)
    const text = await page.locator('body').innerText()

    expect(text).toContain('User-agent: Google-Extended')
    expect(text).toContain('Allow: /')
    expect(text).toContain('User-agent: GPTBot')
    expect(text).toContain('Disallow: /')
    expect(text).toContain('User-agent: CCBot')
    expect(text).toContain('User-agent: PerplexityBot')
    expect(text).toContain('User-agent: Claude-Web')
    expect(text).toContain('User-agent: Applebot-Extended')
    expect(text).toContain('User-agent: Bytespider')
  })
})

