import { test, expect } from '@playwright/test'

// NOTE: Assumes local dev server running at http://localhost:3000
const BASE = 'http://localhost:3000'

/**
 * Validates Brands index and Brand detail pages SEO elements
 * - Canonical link correctness
 * - Presence of JSON-LD scripts (Breadcrumb on index; Brand + Breadcrumb on detail)
 */
test.describe('Brands SEO', () => {
  test('Brands index has canonical and breadcrumb JSON-LD', async ({ page }) => {
    const url = `${BASE}/brands`
    await page.goto(url)

    const canonical = await page.locator('link[rel="canonical"]').getAttribute('href')
    expect(canonical).toBe(`${BASE}/brands`)

    const ldJson = page.locator('script[type="application/ld+json"]')
    await expect(ldJson).toHaveCountGreaterThan(0)

    const allLd = await ldJson.allTextContents()
    const hasBreadcrumb = allLd.some(t => t.includes('"@type":"BreadcrumbList"') || t.includes('"@type": "BreadcrumbList"'))
    expect(hasBreadcrumb).toBeTruthy()
  })

  test('Brand detail has canonical and Brand + Breadcrumb JSON-LD', async ({ page }) => {
    // Use a known seed brand slug present in local DB/fixtures
    const url = `${BASE}/brands/samsung-uk`
    await page.goto(url)

    const canonical = await page.locator('link[rel="canonical"]').getAttribute('href')
    expect(canonical).toBe(`${BASE}/brands/samsung-uk`)

    const ldJson = page.locator('script[type="application/ld+json"]')
    await expect(ldJson).toHaveCount(2)

    const allLd = await ldJson.allTextContents()
    const hasBrand = allLd.some(t => t.includes('"@type":"Brand"') || t.includes('"@type": "Brand"'))
    const hasBreadcrumb = allLd.some(t => t.includes('"@type":"BreadcrumbList"') || t.includes('"@type": "BreadcrumbList"'))

    expect(hasBrand).toBeTruthy()
    expect(hasBreadcrumb).toBeTruthy()
  })
})

