import { test, expect } from '@playwright/test'

const BASE = process.env.BASE_URL || 'http://localhost:3000'

// Helper to fetch plain text content
async function getBodyText(page: any) {
  return page.locator('body').innerText()
}

test.describe('Robots & AI policy', () => {
  test('robots.txt has correct rules and sitemap host', async ({ page }) => {
    await page.goto(`${BASE}/robots.txt`)
    const text = await getBodyText(page)

    // Agent-specific assertions
    expect(text).toContain('User-Agent: Googlebot')
    expect(text).toContain('User-Agent: Google-Extended')
    expect(text).toContain('User-Agent: GPTBot')
    expect(text).toContain('Disallow: /')

    // Ensure sitemap points to localhost:3000 in local runs
    expect(text).toContain('Sitemap: http://localhost:3000/sitemap.xml')
  })

  test('ai.txt exists and matches policy', async ({ page }) => {
    await page.goto(`${BASE}/ai.txt`)
    const text = await getBodyText(page)

    expect(text).toContain('User-agent: Google-Extended')
    expect(text).toContain('Allow: /')
    expect(text).toContain('User-agent: GPTBot')
    expect(text).toContain('Disallow: /')
  })
})

