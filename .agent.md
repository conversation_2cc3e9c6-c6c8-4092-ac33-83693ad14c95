# Project Overview

- Purpose: Cashback Deals web app with SEO-first product, brand, and retailer pages, robust security, and Supabase-backed data.
- Framework: Next.js 15 (App Router) with TypeScript, React 19, Tailwind CSS 4, shadcn/ui (Radix primitives).
- Data: Supabase (Postgres) via server-side client utilities.
- Observability: Sentry for monitoring.
- Validation: Zod schemas.
- SEO: Structured data, sitemaps, robots, Open Graph.

# Getting Started

- Node version: Use latest LTS compatible with Next 15.
- Install: npm install
- Dev server: npm run dev
- Build: npm run build
- Start (prod): npm run start
- Lint: npm run lint
- Common audits:
  - SEO (local): npm run audit:seo
  - Performance (local): npm run audit:performance
- E2E (Playwright): npm run test:e2e
- Jest tests:
  - All: npm test
  - Coverage: npm run test:coverage
  - CI-optimized: npm run test:ci

# Environment & Configuration

- Central env guard: import '@/lib/env-guard' early (see src/app/layout.tsx) to validate required variables.
- Example env: .env.production.example
- Domains and site URL: src/config/domains.ts
- Sitemap config: src/config/sitemap.ts (page size and URLs)
- Feature flags: src/config/features.ts
- Tailwind: tailwind.config.ts (dark mode class, custom colors, animations)
- TypeScript paths (tsconfig.json):
  - @/* → ./src/*
  - @components/* → ./src/components/*
  - @lib/* → ./src/lib/*
  - @config/* → ./src/config/*
  - @hooks/* → ./src/hooks/*
  - @types/* → ./src/types/*
  - @utils/* → ./src/utils/*

# Key Directories

- src/app: Next.js App Router (routes, pages, API routes, sitemaps, robots)
  - src/app/api/*: Route handlers for brands, products, retailers, search, auth verify, health.
  - src/app/sitemaps/*: Paginated sitemaps for products/brands/retailers + static index.
  - src/app/robots.ts: Robots generation.
  - src/app/layout.tsx: Root layout, providers, SEO metadata, web vitals.
- src/components: UI, layout, pages, SEO, performance, debug utilities.
- src/lib: Core libraries
  - security: auth middleware, CORS, HMAC/JWT utils, IP allowlist, rate limiter, error responses.
  - supabase: server client.
  - validation: schemas (Zod).
  - optimization: query optimizer; monitoring: image performance; cache; sitemap validator; SEO helpers.
- src/config: Feature flags, sitemap constants, domains.
- src/hooks: useDebounce, usePagination, performance optimization, product images; page-level hooks.
- src/types: API, product, brand, retailer, supabase types.
- supabase/migrations: SQL migrations (RLS, updated_at columns).
- tests: Jest and Playwright tests organized by unit, integration, e2e, security, fixtures, setup. See tests/README.md.
- docs: Extensive architecture, SEO, performance, deployment, security, and completed feature archives.

# Important Files

- package.json: Scripts for build/test/audit.
- next.config.ts: Next config scaffold (note: includes an API route runtime comment, keep NextConfig export at bottom).
- middleware.ts: Global middleware protecting /api (except health/public), admin, dashboard with IP allowlist.
- src/app/robots.ts: Robots rules (allow all, disallow /api and /admin). Uses env for sitemap URL.
- src/app/sitemap.ts: Generates sitemap index URLs from Supabase counts and SITEMAP_PAGE_SIZE.
- src/components/layout/SEO.tsx and src/components/seo/*: SEO components and structured data utilities.
- src/components/performance/WebVitals.tsx: Web vitals reporting.
- src/components/debug/*: Performance dashboards.

# Security Practices

- IP allowlist middleware: src/lib/security/ip-allowlist.ts, wired via middleware.ts. Health and public endpoints bypass.
- Rate limiting and DOS protection: src/lib/rateLimiter.ts; tests in tests/security/api/rate-limiting.test.ts.
- CORS: src/lib/security/cors.ts; ensure strict origins and headers.
- Auth: JWT + HMAC utility libs under src/lib/security (and tests under tests/security/auth/*).
- Input validation: Zod schemas in src/lib/validation/schemas.ts; always validate API inputs.
- XSS: Use isomorphic-dompurify when rendering user HTML; tests under tests/security/xss/.
- Headers: See tests/security/api/headers.test.ts for required security headers.

# SEO & Sitemaps

- robots: src/app/robots.ts
- sitemap index builder: src/app/sitemap.ts (uses Supabase counts and SITEMAP_PAGE_SIZE)
- per-entity sitemaps: src/app/sitemaps/{products|brands|retailers}/[page]/route.ts
- static sitemap: src/app/sitemaps/static/route.ts
- components: src/components/seo/* (StructuredData, ProductOpenGraphTags, ContactStructuredData)

# Data Layer & Supabase

- Read-only server client: src/lib/supabase/server.ts
- Data access modules: src/lib/data/{products,brands,retailers,promotions,search}.ts
- Caching: src/lib/cache/* and searchCache.ts
- Types: src/types/* align with DB schema (dbschema.json for reference)
- Migrations: supabase/migrations/*.sql

# Testing

- Frameworks: Jest 30 + Testing Library, Playwright for E2E.
- Organization: See tests/README.md for structure and commands.
- Key configs: jest.config.js, jest.config.ci.js, jest.config.simple.js, playwright.config.ts
- Run common subsets via package.json scripts (test:api, test:metadata, etc.).
- Keep TS test files out of build by honoring tsconfig exclude for tests.

# Conventions & Best Practices

- TypeScript strict mode: true; noEmit; moduleResolution: bundler; React 19.
- Import aliases per tsconfig; prefer absolute imports (@/lib/..., @components/...).
- API routes: Validate inputs with Zod; return standardized error responses from src/lib/security/error-responses.ts.
- Security first: Import env guard early in layout, keep middleware rules updated, prefer server-side Supabase access in route handlers.
- SEO defaults: Use constructMetadata util (src/lib/metadata-utils.ts) in layout and pages.
- Styling: Tailwind utility-first; use shadcn/ui components; keep animations defined in tailwind.config.ts.
- Performance: Use WebVitals and debug dashboards; prefer Suspense and streaming where appropriate.
- Caching: Leverage lib/cache and careful ISR/SSG where feasible; follow queryOptimizer patterns.
- File naming: Use kebab-case for files, PascalCase for React components, .tsx for React.
- Testing: Follow Arrange-Act-Assert; co-locate mocks under tests/__mocks__.

# CI/CD & Deployment

- GitHub Actions: .github/workflows/{ci.yml, ci-full.yml, seo-testing.yml}
- Amplify: amplify.yml and docs/deployment/* for AWS Amplify deployment.
- Sentry: sentry.edge.config.ts, sentry.server.config.ts; configure DSN via env.

# Known Patterns & Tips

- Don’t import client-only deps in server route handlers.
- Use src/lib/requestUtils.ts for consistent request handling utilities.
- Use src/lib/seoTesting.ts and scripts/seo-test.js for SEO checks locally.
- When adding new sitemaps, update SITEMAP_PAGE_SIZE and paginate via [page]/route.ts patterns.
- Keep security tests green before merging (headers, rate limits, xss, auth).

# Migrated Guidance (from CLAUDE.md)

- Development workflow highlights:
  - Prefer SSR/SSG where possible; minimize CSR for SEO-critical content.
  - Maintain clear component architecture separation: layout, page clients, feature components, and low-level UI primitives.
  - Avoid status inflation in PRs; keep branches focused and small.
- Reference docs to consult frequently:
  - docs/technical/ARCHITECTURE.md
  - docs/development/ENVIRONMENT_SETUP.md, BUILD_TROUBLESHOOTING.md, WORKFLOWS.md
  - docs/performance/PERFORMANCE_SEO.md and SEO_* docs under docs/performance/
  - docs/deployment/* for CI/CD and Amplify
  - docs/SECURITY/* and archived security audits for rationale.

# Quick Checklist for New Changes

- Update or create Zod schemas for new inputs.
- Add or update tests in the appropriate tests/* area.
- Ensure security middleware and headers remain compliant with tests.
- Verify SEO metadata and structured data for new/changed pages.
- Update sitemaps and robots if URL structures change.
- Consider performance impacts; monitor Web Vitals locally.
- Update docs if behavior or contracts change.
