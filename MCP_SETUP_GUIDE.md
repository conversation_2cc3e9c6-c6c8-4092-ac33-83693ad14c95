# MCP Servers Setup Guide

## Configuration Complete ✅

Your `.mcp.json` file has been updated with the following MCP servers:

1. **GitHub Integration** - Code repository management
2. **Brave Search** - Web search for research and competitive analysis  
3. **Memory Server** - Persistent context across sessions
4. **Puppeteer** - Web scraping for cashback data

## Required API Keys

To use these servers, you'll need to obtain and configure the following API keys:

### 1. GitHub Personal Access Token
1. Go to GitHub Settings → Developer settings → Personal access tokens → Tokens (classic)
2. Generate new token with these scopes:
   - `repo` (for repository access)
   - `read:org` (for organization access)
   - `read:user` (for user information)
3. Replace `YOUR_GITHUB_TOKEN_HERE` in `.mcp.json`

### 2. Brave Search API Key
1. Visit https://api.search.brave.com/app/keys
2. Sign up/login and create a new API key
3. Replace `YOUR_BRAVE_API_KEY_HERE` in `.mcp.json`

### 3. Memory Server
- No API key required - works out of the box!

### 4. Puppeteer
- No API key required - uses headless Chrome for web scraping

## Installation Steps

1. **Install the MCP servers** (they'll be auto-installed when first used):
   ```bash
   npx @modelcontextprotocol/server-github
   npx @modelcontextprotocol/server-brave-search  
   npx @modelcontextprotocol/server-memory
   npx @modelcontextprotocol/server-puppeteer
   ```

2. **Update your API keys** in `.mcp.json`

3. **Restart Claude Desktop** for changes to take effect

## Next Steps

Once configured, you'll have access to powerful new capabilities:
- Manage GitHub repositories and issues
- Search the web for competitive analysis
- Maintain persistent memory across conversations
- Scrape websites for cashback data

Ready for the demo!