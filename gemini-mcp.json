{"name": "Google Gemini MCP", "description": "Connects to Google Gemini for advanced reasoning and code generation.", "server_command": "npx -y @google/gemini-mcp-server", "version": "0.1.0", "author": "Google", "license": "Apache-2.0", "homepage": "https://github.com/google/gemini-mcp", "repository": {"type": "git", "url": "https://github.com/google/gemini-mcp.git"}, "bugs": {"url": "https://github.com/google/gemini-mcp/issues"}, "keywords": ["mcp", "gemini", "google", "ai"], "mcp_version": "1.0.0", "settings": {"api_key": {"type": "string", "description": "Your Google Gemini API key.", "required": true}}}