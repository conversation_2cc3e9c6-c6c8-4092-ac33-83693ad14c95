# Cashback Deals v2 Project Overview

## Purpose
A Next.js-powered e-commerce comparison platform focused on cashback deals, vouchers, and product comparisons. The application helps users find the best deals across multiple retailers with comprehensive product information and cashback opportunities.

## Technology Stack
- **Framework**: Next.js 15.3.5 with App Router architecture
- **Frontend**: React 19.1.0 with TypeScript (latest stable releases)
- **Database**: Supabase (PostgreSQL) with server-side client utilities
- **UI/UX**: Tailwind CSS 4.x with shadcn/ui components (Radix UI primitives)
- **Styling**: Tailwind CSS with custom themes and animations
- **State Management**: React Query for server state, built-in React state for UI
- **Authentication**: Supabase Auth system
- **Testing**: Jest 30 with React Testing Library, Playwright for E2E
- **Deployment**: AWS Amplify with Cloudflare security layers
- **Monitoring**: Sentry for error tracking and performance monitoring
- **Security**: Multi-layered approach with runtime guard-rails
- **SEO**: Comprehensive structured data, dynamic sitemaps, metadata optimization

## Key Features
- Product comparison across multiple retailers
- Cashback and voucher tracking system  
- Advanced search with PostgreSQL full-text search
- SEO-optimized product, brand, and retailer pages
- Mobile-first responsive design
- Performance-optimized React components
- Comprehensive security implementation
- Rich structured data for search engines