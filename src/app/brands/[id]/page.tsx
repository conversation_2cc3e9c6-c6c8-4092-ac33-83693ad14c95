import { notFound } from 'next/navigation';
import { getBrandPageData } from '@/lib/data/brands';
import BrandClient from './BrandClient';
import { Metadata } from 'next';
import { createServerSupabaseReadOnlyClient } from '@/lib/supabase/server';
import { siteConfig } from '@/lib/metadata-utils';
import { BreadcrumbStructuredData, OrganizationStructuredData } from '@/components/seo/StructuredData';

// Define the props type for both the page and metadata function
interface BrandPageProps {
  params: Promise<{
    id: string;
  }>;
}

// Revalidate every hour
export const revalidate = 3600;

// METADATA GENERATION - Correctly placed in the page file
export async function generateMetadata({ params }: BrandPageProps): Promise<Metadata> {
  const { id } = await params;
  const supabase = createServerSupabaseReadOnlyClient();
  const data = await getBrandPageData(supabase, id);

  if (!data?.brand) {
    return {
      title: 'Brand Not Found',
      description: 'The requested brand could not be found.',
      robots: { index: false, follow: false }
    };
  }

  const { brand } = data;
  const title = `${brand.name} Promotions & Cashback Deals`;
  const description = brand.description || `Find the latest ${brand.name} cashback offers and promotions.`;
  const url = `/brands/${brand.slug || brand.id}`;

  // Structured Data for the <head> (kept for backward compatibility; Next.js may ignore this)
  const structuredData = {
    '@context': 'https://schema.org',
    '@type': 'Brand',
    name: brand.name,
    description: description,
    ...(brand.logoUrl && { logo: brand.logoUrl }),
    url: new URL(url, siteConfig.url).toString(),
    identifier: brand.id
  };

  return {
    title,
    description,
    alternates: {
      // Absolute, self-referencing canonical for brand detail pages
      canonical: new URL(url, siteConfig.url).toString()
    },
    openGraph: {
      title,
      description,
      images: brand.logoUrl ? [brand.logoUrl] : [],
      url,
    },
    twitter: {
      card: 'summary_large_image',
      title,
      description,
      images: brand.logoUrl ? [brand.logoUrl] : [],
    },
    other: {
      // Retained for backward compatibility; actual JSON-LD is rendered via component below
      'script:ld+json': JSON.stringify(structuredData)
    }
  };
}

// Helper to check if a string is a valid UUID
const isUuid = (id: string): boolean => {
  const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i;
  return uuidRegex.test(id);
};

// PAGE COMPONENT - Correctly typed
export default async function BrandPage({ params }: BrandPageProps) {
  const { id } = await params;
  const supabase = createServerSupabaseReadOnlyClient();
  const data = await getBrandPageData(supabase, id);

  if (!data?.brand) {
    notFound();
  }

  // Absolute URL for breadcrumb
  const brandUrl = `/brands/${data.brand.slug || data.brand.id}`;

  return (
    <>
      {/* JSON-LD: Brand entity */}
      <OrganizationStructuredData
        organization={{
          id: data.brand.id,
          name: data.brand.name,
          description: data.brand.description || undefined,
          logoUrl: data.brand.logoUrl || undefined,
          // websiteUrl optional; use brand.websiteUrl if present in dataset
          // @ts-ignore - tolerate absence without breaking JSON-LD
          websiteUrl: (data.brand as any).websiteUrl || undefined
        }}
        organizationType="Brand"
      />

      {/* JSON-LD: BreadcrumbList */}
      <BreadcrumbStructuredData
        items={[
          { name: 'Home', url: '/' },
          { name: 'Brands', url: '/brands' },
          { name: data.brand.name, url: brandUrl }
        ]}
      />

      <BrandClient
        brand={data.brand}
        promotions={data.promotions}
        activePromotions={data.activePromotions}
        expiredPromotions={data.expiredPromotions}
        promotionCount={data.promotionCount}
        activePromotionCount={data.activePromotionCount}
        expiredPromotionCount={data.expiredPromotionCount}
      />
    </>
  );
}
