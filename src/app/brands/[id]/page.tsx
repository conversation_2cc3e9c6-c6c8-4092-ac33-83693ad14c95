import { notFound } from 'next/navigation';
import { getBrandPageData } from '@/lib/data/brands';
import BrandClient from './BrandClient';
import { Metadata } from 'next';
import { createServerSupabaseReadOnlyClient } from '@/lib/supabase/server';
import { generateBrandSEO, generateErrorSEO, withSEOErrorHandling } from '@/lib/seo-utils';
import { BreadcrumbStructuredData, OrganizationStructuredData } from '@/components/seo/StructuredData';

// Define the props type for both the page and metadata function
interface BrandPageProps {
  params: Promise<{
    id: string;
  }>;
}

// Revalidate every hour
export const revalidate = 3600;

// METADATA GENERATION - Using unified SEO utilities
export async function generateMetadata({ params }: BrandPageProps): Promise<Metadata> {
  const { id } = await params;
  
  const { data, errorMetadata } = await withSEOErrorHandling(
    async () => {
      const supabase = createServerSupabaseReadOnlyClient();
      return await getBrandPageData(supabase, id);
    },
    'brand'
  );

  if (!data?.brand || errorMetadata) {
    return errorMetadata || generateErrorSEO('brand');
  }

  const seoResult = generateBrandSEO(data.brand);
  return seoResult.metadata;
}

// PAGE COMPONENT - Using unified SEO utilities
export default async function BrandPage({ params }: BrandPageProps) {
  const { id } = await params;
  
  try {
    const supabase = createServerSupabaseReadOnlyClient();
    const data = await getBrandPageData(supabase, id);

    if (!data?.brand) {
      notFound();
    }

    // Generate SEO data using unified utilities
    const seoResult = generateBrandSEO(data.brand);
    const canonicalUrl = `${seoResult.metadata.alternates?.canonical}`;

    return (
      <>
        {/* JSON-LD: Brand entity */}
        <OrganizationStructuredData
          organization={{
            id: data.brand.id,
            name: data.brand.name,
            description: data.brand.description || undefined,
            logoUrl: data.brand.logoUrl || undefined,
            // Use brand websiteUrl if available, otherwise fallback to canonical page URL
            websiteUrl: (data.brand as any).websiteUrl || canonicalUrl
          }}
          organizationType="Brand"
        />

        {/* JSON-LD: BreadcrumbList */}
        <BreadcrumbStructuredData
          items={seoResult.breadcrumbItems}
        />

        <BrandClient
          brand={data.brand}
          promotions={data.promotions}
          activePromotions={data.activePromotions}
          expiredPromotions={data.expiredPromotions}
          promotionCount={data.promotionCount}
          activePromotionCount={data.activePromotionCount}
          expiredPromotionCount={data.expiredPromotionCount}
        />
      </>
    );
  } catch (error) {
    console.error('Error loading brand page:', error);
    notFound();
  }
}
