/**
 * Brands Listing Page
 *
 * This page uses Static Site Generation (SSG) with revalidation for better performance and SEO.
 * The initial data is fetched at build time and revalidated every hour.
 *
 * @note This component expects brand data in camelCase format from the data layer.
 * All transformations to snake_case have been removed to maintain consistency.
 */

import { getBrands } from '@/lib/data'
import { BrandsClient } from './BrandsClient'
import type { BrandWithDetails } from '@/types/brand'
import { Metadata } from 'next'
import { createServerSupabaseReadOnlyClient } from '@/lib/supabase/server';
import { generateListingSEO, generateErrorSEO, withSEOErrorHandling } from '@/lib/seo-utils';
import { BreadcrumbStructuredData } from '@/components/seo/StructuredData';

// Force dynamic rendering to support pagination
export const dynamic = 'force-dynamic';

// Generate metadata for the brands page using unified SEO utilities
export async function generateMetadata(): Promise<Metadata> {
  const { data, errorMetadata } = await withSEOErrorHandling(
    async () => {
      const supabase = createServerSupabaseReadOnlyClient();
      const result = await getBrands(supabase, 1, 1); // Just get one brand to get the count
      return result;
    },
    'brand'
  );

  if (!data || errorMetadata) {
    return errorMetadata || generateErrorSEO('brand');
  }

  const brandCount = data.pagination?.total || 0;
  const seoResult = generateListingSEO('brands', { itemCount: brandCount });
  return seoResult.metadata;
}

interface BrandsPageProps {
  searchParams: Promise<{
    page?: string;
    search?: string;
    featured?: string;
  }>;
}

/**
 * Brands listing page with server-side rendering and pagination
 * Now using unified SEO utilities for consistent metadata and structured data
 */
export default async function BrandsPage({ searchParams }: BrandsPageProps) {
  try {
    const params = await searchParams;

    // Parse URL parameters
    const page = parseInt(params.page || '1', 10);
    const search = params.search || '';
    const featuredOnly = params.featured === 'true';

    const supabase = createServerSupabaseReadOnlyClient();
    // Fetch brands data with pagination
    const result = await getBrands(supabase, page, 24) // 24 brands per page

    if (!result?.data) {
      throw new Error('Failed to fetch brands data')
    }

    // Transform the API response to match our BrandWithDetails type
    const brands: BrandWithDetails[] = result.data.map(brand => ({
      id: brand.id,
      name: brand.name,
      slug: brand.slug,
      logoUrl: brand.logoUrl || null,
      description: brand.description || null,
      featured: brand.featured || false,
      sponsored: brand.sponsored || false,
      createdAt: brand.createdAt,
      updatedAt: brand.updatedAt,
      version: 0, // Default version if not provided
      // Add any additional fields that might be present
      ...(brand as any)
    })).sort((a, b) => a.name.localeCompare(b.name));

    // Generate SEO data using unified utilities
    const seoResult = generateListingSEO('brands', {
      page,
      itemCount: result.pagination?.total || 0,
      search: search || undefined,
      totalPages: result.pagination?.totalPages,
      hasNext: result.pagination?.hasNext,
      hasPrev: result.pagination?.hasPrev,
    });

    return (
      <>
        {/* JSON-LD: BreadcrumbList using unified SEO utilities */}
        <BreadcrumbStructuredData items={seoResult.breadcrumbItems} />

        <BrandsClient
          initialBrands={brands}
          initialPage={page}
          initialSearch={search}
          initialFeaturedOnly={featuredOnly}
          pagination={result.pagination}
        />
      </>
    );
  } catch (error) {
    console.error('Error in BrandsPage:', error)
    // The error will be caught by the error boundary
    throw error
  }
}

// Client components and functionality have been moved to BrandsClient.tsx
