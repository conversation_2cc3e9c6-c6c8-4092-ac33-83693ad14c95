/**
 * Brands Listing Page
 *
 * This page uses Static Site Generation (SSG) with revalidation for better performance and SEO.
 * The initial data is fetched at build time and revalidated every hour.
 *
 * @note This component expects brand data in camelCase format from the data layer.
 * All transformations to snake_case have been removed to maintain consistency.
 */

import { getBrands } from '@/lib/data'
import { BrandsClient } from './BrandsClient'
import type { Brand } from '@/types/database'
import type { BrandWithDetails } from '@/types/brand'
import { Metadata } from 'next'
import { createServerSupabaseReadOnlyClient } from '@/lib/supabase/server';
import { SITE_URL } from '@/config/domains';
import { BreadcrumbStructuredData } from '@/components/seo/StructuredData';

// Force dynamic rendering to support pagination
export const dynamic = 'force-dynamic';

// Generate metadata for the brands page
export async function generateMetadata(): Promise<Metadata> {
  const supabase = createServerSupabaseReadOnlyClient();
  const result = await getBrands(supabase, 1, 1) // Just get one brand to get the count
  const brandCount = result?.pagination?.total || 0

  const title = 'Shop by Brand | Cashback Deals'
  const description = `Browse our collection of ${brandCount} brands offering exclusive cashback deals and discounts.`
  const url = '/brands'

  // Generate structured data for the collection page
  const structuredData = {
    '@context': 'https://schema.org',
    '@type': 'CollectionPage',
    name: title,
    description: description,
    url: new URL(url, SITE_URL).toString(),
    mainEntity: {
      '@type': 'ItemList',
      numberOfItems: brandCount,
      itemListElement: {
        '@type': 'ListItem',
        position: 1,
        item: {
          '@type': 'ItemList',
          itemListElement: Array.from({ length: Math.min(brandCount, 5) }, (_, i) => ({
            '@type': 'ListItem',
            position: i + 1,
            item: {
              '@type': 'Brand',
              name: `Brand ${i + 1}`,
              url: `${SITE_URL}/brands/brand-${i + 1}`
            }
          }))
        }
      }
    }
  };

  return {
    title,
    description,
    alternates: {
      // Absolute, self-referencing canonical for the brands index
      canonical: new URL(url, SITE_URL).toString()
    },
    openGraph: {
      title,
      description,
      url,
      type: 'website',
      siteName: 'Cashback Deals',
      locale: 'en_US'
    },
    twitter: {
      card: 'summary_large_image',
      title,
      description
    },
    other: {
      'script:ld+json': JSON.stringify(structuredData)
    }
  };
}

interface BrandsPageProps {
  searchParams: Promise<{
    page?: string;
    search?: string;
    featured?: string;
  }>;
}

/**
 * Brands listing page with server-side rendering and pagination
 *
 * This page supports URL-based pagination and filtering.
 * It shows a list of brands grouped by their first letter with server-side
 * pagination and client-side search capabilities.
 */
export default async function BrandsPage({ searchParams }: BrandsPageProps) {
  try {
    const params = await searchParams;

    // Parse URL parameters
    const page = parseInt(params.page || '1', 10);
    const search = params.search || '';
    const featuredOnly = params.featured === 'true';

    const supabase = createServerSupabaseReadOnlyClient();
    // Fetch brands data with pagination
    const result = await getBrands(supabase, page, 24) // 24 brands per page

    if (!result?.data) {
      throw new Error('Failed to fetch brands data')
    }

    // Transform the API response to match our BrandWithDetails type
    const brands: BrandWithDetails[] = result.data.map(brand => ({
      id: brand.id,
      name: brand.name,
      slug: brand.slug,
      logoUrl: brand.logoUrl || null,
      description: brand.description || null,
      featured: brand.featured || false,
      sponsored: brand.sponsored || false,
      createdAt: brand.createdAt,
      updatedAt: brand.updatedAt,
      version: 0, // Default version if not provided
      // Add any additional fields that might be present
      ...(brand as any)
    })).sort((a, b) => a.name.localeCompare(b.name));

    return (
      <>
        {/* JSON-LD: BreadcrumbList for brands index */}
        <BreadcrumbStructuredData
          items={[
            { name: 'Home', url: '/' },
            { name: 'Brands', url: '/brands' }
          ]}
        />

        <BrandsClient
          initialBrands={brands}
          initialPage={page}
          initialSearch={search}
          initialFeaturedOnly={featuredOnly}
          pagination={result.pagination}
        />
      </>
    );
  } catch (error) {
    console.error('Error in BrandsPage:', error)
    // The error will be caught by the error boundary
    throw error
  }
}

// Client components and functionality have been moved to BrandsClient.tsx
