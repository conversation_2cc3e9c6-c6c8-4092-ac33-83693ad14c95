import { BreadcrumbStructuredData, ProductListStructuredData, OrganizationStructuredData, ProductStructuredData } from '@/components/seo/StructuredData';
import { Suspense } from 'react';
import { generateHomepageSEO, generateErrorSEO, withSEOErrorHandling } from '@/lib/seo-utils';
import { getFeaturedProducts } from '@/lib/data/products';
import { getFeaturedPromotions } from '@/lib/data/promotions';
import { getFeaturedBrands } from '@/lib/data/brands';
import { getFeaturedRetailers } from '@/lib/data/retailers';
import { HomePageClient } from '@/components/pages/HomePageClient';
import { WebSiteStructuredData } from '@/components/seo/StructuredData';

// Generate dynamic metadata for SEO optimization using unified SEO utilities
export async function generateMetadata() {
  const { data, errorMetadata } = await withSEOErrorHandling(
    async () => {
      // Homepage doesn't need complex data fetching for metadata
      return { success: true };
    },
    'home'
  );

  if (!data || errorMetadata) {
    return errorMetadata || generateErrorSEO('home');
  }

  const seoResult = generateHomepageSEO();
  return seoResult.metadata;
}

// Loading skeleton component for better UX during data fetching
function HomePageSkeleton() {
  return (
    <div className="flex flex-col gap-12 pb-20">
      {/* Hero Section Skeleton */}
      <section className="bg-gradient-to-r from-primary/10 via-secondary/10 to-background py-20">
        <div className="container">
          <div className="max-w-2xl">
            <div className="h-16 bg-gray-300 rounded mb-6 animate-pulse"></div>
            <div className="h-6 bg-gray-300 rounded mb-4 animate-pulse"></div>
            <div className="h-6 bg-gray-300 rounded w-3/4 mb-8 animate-pulse"></div>
            <div className="h-12 bg-gray-300 rounded w-40 animate-pulse"></div>
          </div>
        </div>
      </section>

      {/* Featured Promotions Skeleton */}
      <section className="container py-12">
        <div className="h-8 bg-gray-300 rounded w-80 mx-auto mb-8 animate-pulse"></div>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          {Array.from({ length: 6 }).map((_, i) => (
            <div key={i} className="card p-6 animate-pulse">
              <div className="flex items-center gap-4 mb-4">
                <div className="h-12 w-12 rounded-full bg-gray-300" />
                <div className="space-y-2">
                  <div className="h-4 w-24 bg-gray-300 rounded" />
                  <div className="h-3 w-16 bg-gray-300 rounded" />
                </div>
              </div>
              <div className="space-y-2">
                <div className="h-4 w-full bg-gray-300 rounded" />
                <div className="h-3 w-1/2 bg-gray-300 rounded" />
              </div>
            </div>
          ))}
        </div>
      </section>

      {/* Brands Skeleton */}
      <section className="container py-12">
        <div className="h-8 bg-gray-300 rounded w-60 mx-auto mb-8 animate-pulse"></div>
        <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-6">
          {Array.from({ length: 6 }).map((_, i) => (
            <div key={i} className="card p-4 text-center animate-pulse">
              <div className="h-12 w-12 bg-gray-300 rounded mx-auto mb-2" />
              <div className="h-4 bg-gray-300 rounded" />
            </div>
          ))}
        </div>
      </section>
    </div>
  );
}

import { createServerSupabaseReadOnlyClient } from '@/lib/supabase/server';

// Main homepage component - Server Component for optimal SEO
export default async function HomePage() {
  try {
    const supabase = createServerSupabaseReadOnlyClient();
    // Server-side data fetching for improved SEO and Core Web Vitals
    // Fetch all data in parallel for better performance
    // Get featured data in parallel
    const [featuredProducts, featuredPromotions, featuredBrands, featuredRetailers] = await Promise.all([
      getFeaturedProducts(supabase, 8), // Get 8 featured products for homepage
      getFeaturedPromotions(supabase, 6), // Get 6 featured promotions for homepage
      getFeaturedBrands(supabase, 6),   // Get 6 featured brands
      getFeaturedRetailers(supabase, 4) // Get 4 featured retailers
    ]);

    // Compute fallback purchase end date once on server
    const fallbackPurchaseEndDate = new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString();

    // Generate SEO data using unified utilities
    const seoResult = generateHomepageSEO({
      featuredProductsCount: featuredProducts.length,
      featuredBrandsCount: featuredBrands.length,
      featuredPromotionsCount: featuredPromotions.length,
    });

    return (
      <>
        {/* WebSite structured data for enhanced SEO */}
        <WebSiteStructuredData />

        {/* JSON-LD: BreadcrumbList using unified SEO utilities */}
        <BreadcrumbStructuredData items={seoResult.breadcrumbItems} />

        {/* Product list structured data for featured products */}
        <ProductListStructuredData products={featuredProducts} />

        {/* Organization structured data for featured brands */}
        {featuredBrands.map((brand) => (
          <OrganizationStructuredData key={brand.id} organization={brand} organizationType="Brand" />
        ))}

        {/* Product structured data for each featured product */}
        {featuredProducts.map((product) => (
          <ProductStructuredData key={product.id} product={product} fallbackPurchaseEndDate={fallbackPurchaseEndDate} />
        ))}

        {/* Suspense boundary for progressive loading */}
        <Suspense fallback={<HomePageSkeleton />}>
          <HomePageClient
            featuredProducts={featuredProducts}
            featuredPromotions={featuredPromotions}
            featuredBrands={featuredBrands}
            featuredRetailers={featuredRetailers}
            fallbackPurchaseEndDate={fallbackPurchaseEndDate}
          />
        </Suspense>
      </>
    );
  } catch (error) {
    console.error('Error loading homepage data:', error);
    
    // Fallback content in case of data fetching errors
    return (
      <>
        <WebSiteStructuredData />
        <div className="container py-20 text-center">
          <h1 className="text-4xl font-bold text-primary mb-6">
            Welcome to RebateRay
          </h1>
          <p className="text-lg text-foreground/70 mb-8">
            Discover the best cashback deals and offers from top retailers.
          </p>
          <p className="text-foreground/60">
            We&apos;re currently loading the latest deals. Please refresh the page or try again later.
          </p>
        </div>
      </>
    );
  }
}
