// src/app/products/[id]/page.tsx - Product detail page with Server-Side Rendering
// Converted from client component to server component for improved SEO and Core Web Vitals

import { Suspense } from 'react';
import { notFound } from 'next/navigation';
import Link from 'next/link';
import { generateProductSEO, generateErrorSEO, withSEOErrorHandling } from '@/lib/seo-utils';
import { getProductPageData } from '@/lib/data/products';
import { ProductPageClient } from '@/components/pages/ProductPageClient';
import { ProductStructuredData, BreadcrumbStructuredData } from '@/components/seo/StructuredData';

import { createServerSupabaseReadOnlyClient } from '@/lib/supabase/server';

interface ProductPageProps {
  params: Promise<{ id: string }>;
  searchParams: Promise<{ returnTo?: string }>;
}

export async function generateMetadata({ params }: { params: Promise<{ id: string }> }) {
  const resolvedParams = await params;
  
  const { data, errorMetadata } = await withSEOErrorHandling(
    async () => {
      const supabase = createServerSupabaseReadOnlyClient();
      return await getProductPageData(supabase, resolvedParams.id);
    },
    'product'
  );

  if (!data?.product || errorMetadata) {
    return errorMetadata || generateErrorSEO('product');
  }

  const seoResult = generateProductSEO(data.product);
  return seoResult.metadata;
}

function ProductPageSkeleton() {
  return (
    <div className="container py-12">
      <div>
        <div className="h-6 bg-gray-300 rounded w-32 mb-8 animate-pulse"></div>
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
          <div className="h-96 bg-gray-300 rounded animate-pulse"></div>
          <div className="space-y-4">
            <div className="h-8 bg-gray-300 rounded animate-pulse"></div>
            <div className="h-6 bg-gray-300 rounded w-3/4 animate-pulse"></div>
            <div className="h-4 bg-gray-300 rounded w-1/2 animate-pulse"></div>
            <div className="h-16 bg-gray-300 rounded animate-pulse"></div>
            <div className="h-12 bg-gray-300 rounded w-40 animate-pulse"></div>
          </div>
        </div>
        <div className="mb-8">
          <div className="h-6 bg-gray-300 rounded w-48 mb-4 animate-pulse"></div>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {Array.from({ length: 3 }).map((_, i) => (
              <div key={i} className="h-32 bg-gray-300 rounded animate-pulse"></div>
            ))}
          </div>
        </div>
        <div>
          <div className="h-6 bg-gray-300 rounded w-40 mb-4 animate-pulse"></div>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            {Array.from({ length: 4 }).map((_, i) => (
              <div key={i} className="h-64 bg-gray-300 rounded animate-pulse"></div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
}

export default async function ProductPage({ params, searchParams }: ProductPageProps) {
  const resolvedParams = await params;
  const resolvedSearchParams = await searchParams;

  try {
    const supabase = createServerSupabaseReadOnlyClient();
    const result = await getProductPageData(supabase, resolvedParams.id);

    if (!result?.product) {
      notFound();
    }

    // Generate SEO data using unified utilities
    const seoResult = generateProductSEO(result.product);

    return (
      <>
        <ProductStructuredData product={result.product} retailerOffers={result.product.retailerOffers} />
        <BreadcrumbStructuredData items={seoResult.breadcrumbItems} />
        <nav aria-label="Breadcrumb" className="container mb-4">
          <ol className="flex flex-wrap items-center gap-1 text-sm text-muted-foreground [&>li:not(:last-child)]:after:content-['/'] [&>li:not(:last-child)]:after:mx-1">
            {seoResult.breadcrumbItems.map((item, index) => (
              <li key={index}>
                {index === seoResult.breadcrumbItems.length - 1 ? (
                  <span aria-current="page" className="text-foreground">
                    {item.name}
                  </span>
                ) : (
                  <Link href={item.url} className="hover:text-foreground">
                    {item.name}
                  </Link>
                )}
              </li>
            ))}
          </ol>
        </nav>
        <Suspense fallback={<ProductPageSkeleton />}>
          <ProductPageClient
            product={result.product}
            similarProducts={result.similarProducts || []}
            returnTo={resolvedSearchParams.returnTo}
          />
        </Suspense>
      </>
    );
  } catch {
    notFound();
  }
}
