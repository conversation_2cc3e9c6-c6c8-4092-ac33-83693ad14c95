// src/app/products/[id]/page.tsx - Product detail page with Server-Side Rendering
// Converted from client component to server component for improved SEO and Core Web Vitals

import { Suspense } from 'react';
import { notFound } from 'next/navigation';
import Link from 'next/link';
import { constructMetadata } from '@/lib/metadata-utils';
import { getProductPageData } from '@/lib/data/products';
import { ProductPageClient } from '@/components/pages/ProductPageClient';
import { ProductStructuredData, BreadcrumbStructuredData } from '@/components/seo/StructuredData';
import { SITE_URL } from '@/config/domains';

import { createServerSupabaseReadOnlyClient } from '@/lib/supabase/server';

interface ProductPageProps {
  params: Promise<{ id: string }>;
  searchParams: Promise<{ returnTo?: string }>;
}

export async function generateMetadata({ params }: { params: Promise<{ id: string }> }) {
  const resolvedParams = await params;
  try {
    const supabase = createServerSupabaseReadOnlyClient();
    const result = await getProductPageData(supabase, resolvedParams.id);

    if (!result?.product) {
      return constructMetadata({
        title: 'Product Not Found',
        description: 'The requested product could not be found.',
        noIndex: true,
      });
    }

    const product = result.product;
    const offers = product.retailerOffers || [];
    const validOffers = offers.filter(o => typeof o.price === 'number' && o.price > 0);

    const title = `${product.name} - Best Cashback Deals`;
    const description = product.description
      ? `${product.description.substring(0, 155)}...`
      : `Get cashback on ${product.name} from ${product.brand?.name || 'top retailers'}. Compare prices and save money with our exclusive offers.`;

    // Calculate primary price (lowest valid offer)
    const primaryPrice = validOffers.length > 0 
      ? Math.min(...validOffers.map(offer => offer.price as number))
      : null;

    // Get primary image
    const primaryImage = product.images && product.images.length > 0
      ? (product.images[0].startsWith('http') 
          ? product.images[0] 
          : `${process.env.NEXT_PUBLIC_SUPABASE_URL}/storage/v1/object/public/${product.images[0]}`)
      : product.brand?.logoUrl || undefined;

    // Build plural_title (product variations/similar names)
    const buildPluralTitle = () => {
      const titles = [product.name];
      
      // Add brand + product name variation
      if (product.brand?.name) {
        titles.push(`${product.brand.name} ${product.name}`);
      }
      
      // Add model number if available
      if (product.specifications?.model_number || product.specifications?.sku) {
        const modelNumber = product.specifications?.model_number || product.specifications?.sku;
        titles.push(modelNumber);
      }
      
      // Add category-specific variation
      if (product.category?.name) {
        titles.push(`${product.name} - ${product.category.name}`);
      }
      
      // Remove duplicates and join
      return [...new Set(titles)].join(', ');
    };

    const pluralTitle = buildPluralTitle();

    // 🚨 CRITICAL: FINAL OPENGRAPH METADATA IMPLEMENTATION 
    // DO NOT MODIFY WITHOUT EXPLICIT CONFIRMATION
    // ALL ATTEMPTS TO USE og:type="product" FAILED NEXT.JS VALIDATION
    // THIS IS THE STABLE SOLUTION THAT ELIMINATES VALIDATION ERRORS
    //
    // Technical Context: Next.js metadata API rejects og:type="product" in ALL forms.
    // This implementation provides basic OpenGraph metadata for social sharing
    // while preserving comprehensive SEO through JSON-LD structured data.
    //
    // CRITICAL: constructMetadata already includes Twitter Card configuration internally
    // Issue resolved: Complete removal of problematic og:type and product-specific tags
    return constructMetadata({
      title,
      description,
      image: primaryImage,
      pathname: `/products/${product.slug || product.id}`,
      // Basic OpenGraph configuration - NO product-specific metadata
      openGraph: {
        // NO type: 'product' - completely removed due to Next.js limitations
        title,
        description,
        url: `${SITE_URL}/products/${product.slug || product.id}`,
        siteName: 'RebateRay',
        locale: 'en_GB',
        images: primaryImage ? [{ url: primaryImage, alt: `${product.name} product image` }] : undefined,
      },
      // NOTE: Twitter Card configuration is handled automatically by constructMetadata
      // NOTE: Product-specific metadata removed entirely due to Next.js validation restrictions
      // SEO is maintained through comprehensive JSON-LD structured data below
    });
  } catch {
    return constructMetadata({
      title: 'Product Not Found',
      description: 'The requested product could not be found.',
      noIndex: true,
    });
  }
}

function ProductPageSkeleton() {
  return (
    <div className="container py-12">
      <div>
        <div className="h-6 bg-gray-300 rounded w-32 mb-8 animate-pulse"></div>
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
          <div className="h-96 bg-gray-300 rounded animate-pulse"></div>
          <div className="space-y-4">
            <div className="h-8 bg-gray-300 rounded animate-pulse"></div>
            <div className="h-6 bg-gray-300 rounded w-3/4 animate-pulse"></div>
            <div className="h-4 bg-gray-300 rounded w-1/2 animate-pulse"></div>
            <div className="h-16 bg-gray-300 rounded animate-pulse"></div>
            <div className="h-12 bg-gray-300 rounded w-40 animate-pulse"></div>
          </div>
        </div>
        <div className="mb-8">
          <div className="h-6 bg-gray-300 rounded w-48 mb-4 animate-pulse"></div>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {Array.from({ length: 3 }).map((_, i) => (
              <div key={i} className="h-32 bg-gray-300 rounded animate-pulse"></div>
            ))}
          </div>
        </div>
        <div>
          <div className="h-6 bg-gray-300 rounded w-40 mb-4 animate-pulse"></div>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            {Array.from({ length: 4 }).map((_, i) => (
              <div key={i} className="h-64 bg-gray-300 rounded animate-pulse"></div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
}

export default async function ProductPage({ params, searchParams }: ProductPageProps) {
  const resolvedParams = await params;
  const resolvedSearchParams = await searchParams;

  try {
    const supabase = createServerSupabaseReadOnlyClient();
    const result = await getProductPageData(supabase, resolvedParams.id);

    if (!result?.product) {
      notFound();
    }

    const productUrl = `/products/${result.product.slug || result.product.id}`;
    const breadcrumbItems = result.product.category?.slug
      ? [
          { name: 'Home', url: '/' },
          {
            name: result.product.category.name,
            url: `/search?category=${result.product.category.slug}`,
          },
          { name: result.product.name, url: productUrl },
        ]
      : [
          { name: 'Home', url: '/' },
          { name: result.product.name, url: productUrl },
        ];

    return (
      <>
        <ProductStructuredData product={result.product} retailerOffers={result.product.retailerOffers} />
        <BreadcrumbStructuredData items={breadcrumbItems} />
        <nav aria-label="Breadcrumb" className="container mb-4">
          <ol className="flex flex-wrap items-center gap-1 text-sm text-muted-foreground [&>li:not(:last-child)]:after:content-['/'] [&>li:not(:last-child)]:after:mx-1">
            <li>
              <Link href="/" className="hover:text-foreground">
                Home
              </Link>
            </li>
            {result.product.category?.slug && (
              <li>
                <Link
                  href={`/search?category=${result.product.category.slug}`}
                  className="hover:text-foreground"
                >
                  {result.product.category.name}
                </Link>
              </li>
            )}
            <li aria-current="page" className="text-foreground">
              {result.product.name}
            </li>
          </ol>
        </nav>
        <Suspense fallback={<ProductPageSkeleton />}>
          <ProductPageClient
            product={result.product}
            similarProducts={result.similarProducts || []}
            returnTo={resolvedSearchParams.returnTo}
          />
        </Suspense>
      </>
    );
  } catch {
    notFound();
  }
}
