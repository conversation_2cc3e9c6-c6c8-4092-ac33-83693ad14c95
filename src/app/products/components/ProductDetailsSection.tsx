import React from 'react';
import { motion } from 'framer-motion';


interface ProductDetailsProps {
    description: string;
}

export function ProductDetailsSection({ description }: ProductDetailsProps) {

    return (
        <motion.section
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            className="mt-12"
            aria-labelledby="product-details-heading"
        >
            <h2 id="product-details-heading" className="text-2xl font-bold text-primary mb-6">
                Product Details
            </h2>
            
            {/* Product Description - Always visible, no accordion */}
            <div className="bg-white border rounded-lg overflow-hidden">
                <div className="px-6 py-4 border-b bg-gray-50">
                    <h3 className="text-lg font-semibold">Description</h3>
                </div>
                <div className="px-6 py-6">
                    <div className="prose max-w-none text-foreground/80">
                        <p>{description}</p>
                    </div>
                </div>
            </div>
        </motion.section>
    );
}