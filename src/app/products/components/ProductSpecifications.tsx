'use client';

import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { ChevronDown, ChevronUp, Zap } from 'lucide-react';

interface Specification {
    [key: string]: string | undefined;
}

interface ProductSpecificationsProps {
    specifications: Specification | null;
}

export function ProductSpecifications({ specifications }: ProductSpecificationsProps) {
    const [expandedSections, setExpandedSections] = useState<Record<string, boolean>>({});

    // Function to format specification keys for display
    const formatSpecKey = (key: string): string => {
        return key
            .replace(/_/g, ' ')
            .split(' ')
            .map(word => word.charAt(0).toUpperCase() + word.slice(1))
            .join(' ');
    };

    // Enhanced function to format specification values with better list styling
    const formatSpecValue = (value: string | undefined): React.ReactNode => {
        if (!value) return '';
        
        // If value starts with # characters, render as styled list items
        if (value.includes('# ')) {
            const items = value.split('\n').filter(item => item.trim().startsWith('# '));
            return (
                <ul className="space-y-2 mt-1">
                    {items.map((item, i) => (
                        <li key={i} className="flex items-start">
                            <span className="inline-block w-1.5 h-1.5 bg-blue-500 rounded-full mr-3 mt-2 flex-shrink-0"></span>
                            <span className="text-gray-700 text-sm leading-relaxed">
                                {item.replace('# ', '')}
                            </span>
                        </li>
                    ))}
                </ul>
            );
        }
        
        // If value contains numbered list format (1., 2., etc.)
        if (/^\d+\.\s/.test(value)) {
            const items = value.split('\n').filter(item => /^\d+\.\s/.test(item.trim()));
            return (
                <ol className="space-y-2 mt-1">
                    {items.map((item, i) => (
                        <li key={i} className="flex items-start">
                            <span className="inline-flex items-center justify-center w-5 h-5 bg-blue-500 text-white text-xs font-medium rounded-full mr-3 mt-0.5 flex-shrink-0">
                                {i + 1}
                            </span>
                            <span className="text-gray-700 text-sm leading-relaxed">
                                {item.replace(/^\d+\.\s/, '')}
                            </span>
                        </li>
                    ))}
                </ol>
            );
        }
        
        // Otherwise, return as well-formatted text with proper line spacing
        return (
            <div className="text-gray-700 text-sm leading-relaxed">
                {value.split('\n').map((line, i) => (
                    <React.Fragment key={i}>
                        {line}
                        {i < value.split('\n').length - 1 && <br />}
                    </React.Fragment>
                ))}
            </div>
        );
    };

    // Group specifications into logical categories
    const specCategories = React.useMemo(() => {
        if (!specifications) return {};
        
        const categories: Record<string, Record<string, string>> = {
            'Main Features': {},
            'Technical Specifications': {},
            'Physical Specifications': {},
            'Additional Information': {}
        };
        
        Object.entries(specifications).forEach(([key, value]) => {
            if (value === undefined) return;
            if (key === 'price' || key.startsWith('product_image_') || key === 'product_description') return;
            
            const stringValue: string = value;
            
            // Main Features - key product highlights and benefits
            if (['features', 'smart_features', 'highlights', 'benefits', 'key_features'].includes(key)) {
                categories['Main Features'][key] = stringValue;
            } 
            // Physical Specifications - dimensions, appearance, materials
            else if (['dimensions', 'color_finish', 'materials', 'weight', 'size', 'colour', 'finish', 'material'].includes(key)) {
                categories['Physical Specifications'][key] = stringValue;
            }
            // Technical Specifications - technical details, ratings, model info
            else if (['technical_specs', 'energy_rating', 'model_number', 'power', 'voltage', 'capacity', 'performance'].includes(key)) {
                categories['Technical Specifications'][key] = stringValue;
            }
            // Additional Information - everything else
            else {
                categories['Additional Information'][key] = stringValue;
            }
        });
        
        // Remove empty categories
        Object.keys(categories).forEach(category => {
            if (Object.keys(categories[category]).length === 0) {
                delete categories[category];
            }
        });
        
        // Debug logging (remove in production)
        if (process.env.NODE_ENV === 'development') {
            console.log('ProductSpecifications Debug:', {
                totalSpecs: Object.keys(specifications || {}).length,
                availableKeys: Object.keys(specifications || {}),
                categories: Object.keys(categories),
                categoryCounts: Object.fromEntries(Object.entries(categories).map(([k, v]) => [k, Object.keys(v).length]))
            });
        }
        
        return categories;
    }, [specifications]);

    // Initialize all sections as expanded on first load
    React.useEffect(() => {
        const categoryNames = Object.keys(specCategories);
        if (categoryNames.length > 0) {
            setExpandedSections(prev => {
                const newState = { ...prev };
                categoryNames.forEach(category => {
                    if (!(category in newState)) {
                        newState[category] = true; // Start expanded
                    }
                });
                return newState;
            });
        }
    }, [specCategories]);

    const toggleSection = (categoryName: string) => {
        setExpandedSections(prev => ({
            ...prev,
            [categoryName]: !prev[categoryName]
        }));
    };

    // Don't render if no specifications
    if (!specifications || Object.keys(specCategories).length === 0) {
        return null;
    }

    const categoryEntries = Object.entries(specCategories);

    return (
        <motion.section
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, ease: "easeOut" }}
            className="mt-12"
            aria-labelledby="product-specifications-heading"
        >
            {/* Clean, modern section header */}
            <div className="mb-10">
                <div className="flex items-center mb-3">
                    <Zap className="h-5 w-5 text-blue-500 mr-2" />
                    <h2 
                        id="product-specifications-heading" 
                        className="text-2xl font-bold text-gray-900"
                    >
                        Specifications
                    </h2>
                </div>
                <p className="text-gray-600 text-sm max-w-2xl">
                    Complete technical details and features
                   
                </p>
            </div>

            {/* SEO Note: All specifications are now always present in HTML for search engine crawling */}

            {/* Specifications in clean single-column layout - All content crawlable */}
            <div className="space-y-8 specifications-container">
                {categoryEntries.map(([category, specs], categoryIndex) => {
                    return (
                        <motion.div
                            key={category}
                            initial={{ opacity: 0, y: 10 }}
                            animate={{ opacity: 1, y: 0 }}
                            transition={{ duration: 0.3, delay: categoryIndex * 0.1 }}
                            className="block"
                        >
                            {/* Simplified category header with better hierarchy */}
                            <div className="mb-6">
                                <button
                                    onClick={() => toggleSection(category)}
                                    className="group flex items-center justify-between w-full text-left focus:outline-none focus:ring-2 focus:ring-blue-500/20 focus:ring-offset-2 rounded-lg px-3 py-4 -mx-3 min-h-[60px] hover:bg-gray-50/50 transition-colors"
                                    aria-expanded={expandedSections[category] === true}
                                    aria-controls={`${category.replace(/\s+/g, '-').toLowerCase()}-content`}
                                >
                                    <div className="flex items-center">
                                        <h3 className="text-lg font-semibold text-gray-900 group-hover:text-blue-600 transition-colors">
                                            {category}
                                        </h3>
                                        <span className="ml-3 text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded-full">
                                            {Object.keys(specs).length}
                                        </span>
                                    </div>
                                    <div className="flex items-center justify-center w-8 h-8 rounded-lg group-hover:bg-blue-50 transition-colors">
                                        {expandedSections[category] === true ? (
                                            <ChevronUp className="h-5 w-5 text-gray-400 group-hover:text-blue-600 transition-colors" />
                                        ) : (
                                            <ChevronDown className="h-5 w-5 text-gray-400 group-hover:text-blue-600 transition-colors" />
                                        )}
                                    </div>
                                </button>
                            </div>
                            
                            {/* Clean specifications list */}
                            <AnimatePresence>
                                {expandedSections[category] === true && (
                                    <motion.div
                                        id={`${category.replace(/\s+/g, '-').toLowerCase()}-content`}
                                        initial={{ opacity: 0, height: 0 }}
                                        animate={{ opacity: 1, height: 'auto' }}
                                        exit={{ opacity: 0, height: 0 }}
                                        transition={{ duration: 0.3, ease: "easeInOut" }}
                                        className="bg-white border border-gray-200/60 rounded-xl overflow-hidden"
                                    >
                                        {Object.entries(specs).map(([key, value], index) => (
                                            <div 
                                                key={key} 
                                                className={`px-6 py-4 border-b border-gray-100/70 last:border-b-0 ${
                                                    index % 2 === 0 ? 'bg-white' : 'bg-gray-50/30'
                                                }`}
                                            >
                                                <div className="grid grid-cols-1 lg:grid-cols-3 gap-3 lg:gap-6 items-start">
                                                    <dt className="font-medium text-gray-900 text-sm lg:text-base">
                                                        {formatSpecKey(key)}
                                                    </dt>
                                                    <dd className="lg:col-span-2 text-gray-700 min-w-0">
                                                        {formatSpecValue(value)}
                                                    </dd>
                                                </div>
                                            </div>
                                        ))}
                                    </motion.div>
                                )}
                            </AnimatePresence>
                        </motion.div>
                    );
                })}
            </div>
        </motion.section>
    );
}