'use client'

import React, { useMemo, useState } from 'react'
import { motion } from 'framer-motion'
import { 
    Tag, 
    ArrowUpDown, 
    Filter, 
    Check,
    ShoppingCart,
    ChevronDown
} from 'lucide-react'
import Image from 'next/image'
import Link from 'next/link'
import { featureFlags } from '@config/features'
import { formatPrice, sortOptions, defaultSortOption, type SortOption } from '@lib/utils'
import { Store } from 'lucide-react';


interface RetailerOffer {
    retailer: {
        name: string;
        logoUrl: string | null;
    };
    price: number;
    stockStatus: string;
    url: string;
    createdAt: string;
}

interface PriceComparisonProps {
    retailerOffers: RetailerOffer[];
}

type StockFilterOption = 'all' | 'in_stock' | 'low_stock' | 'out_of_stock' | 'pre_order' | 'discontinued';

interface FilterState {
    stockStatus: StockFilterOption;
    retailers: string[];
}

export function PriceComparison({ retailerOffers }: PriceComparisonProps) {
    const [sortBy, setSortBy] = useState<SortOption>(defaultSortOption);
    const [showFilters, setShowFilters] = useState(false);
    const [filters, setFilters] = useState<FilterState>({
        stockStatus: 'all',
        retailers: [],
    });

    // Get unique retailers for filter options
    const uniqueRetailers = useMemo(() => {
        return [...new Set(retailerOffers.map(offer => offer.retailer.name))].sort();
    }, [retailerOffers]);

    // Get stock status counts
    const stockStatusCounts = useMemo(() => {
        const counts = {
            all: retailerOffers.length,
            in_stock: 0,
            low_stock: 0,
            out_of_stock: 0,
            pre_order: 0,
            discontinued: 0,
        };

        retailerOffers.forEach(offer => {
            if (offer.stockStatus === 'in_stock') counts.in_stock++;
            else if (offer.stockStatus === 'low_stock' || offer.stockStatus === 'limited_stock') counts.low_stock++;
            else if (offer.stockStatus === 'out_of_stock') counts.out_of_stock++;
            else if (offer.stockStatus === 'pre_order') counts.pre_order++;
            else if (offer.stockStatus === 'discontinued') counts.discontinued++;
        });

        return counts;
    }, [retailerOffers]);

    // Filter offers based on selected filters
    const filteredOffers = useMemo(() => {
        return retailerOffers.filter(offer => {
            // Filter by stock status
            if (filters.stockStatus !== 'all' && offer.stockStatus !== filters.stockStatus) {
                return false;
            }

            // Filter by retailers
            if (filters.retailers.length > 0 && !filters.retailers.includes(offer.retailer.name)) {
                return false;
            }

            return true;
        });
    }, [retailerOffers, filters]);

    // Sort filtered offers
    const sortedOffers = useMemo(() => {
        const sorted = [...filteredOffers];
        switch (sortBy) {
            case 'price_desc':
                return sorted.sort((a, b) => b.price - a.price);
            case 'price_asc':
                return sorted.sort((a, b) => a.price - b.price);
            case 'newest':
                return sorted.sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime());
            case 'oldest':
                return sorted.sort((a, b) => new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime());
            case 'recommended':
                return featureFlags.search.recommendedFilter
                    ? sorted
                        .filter(offer => ['in_stock', 'low_stock'].includes(offer.stockStatus))
                        .sort((a, b) => a.price - b.price)
                    : sorted;
            default:
                return sorted;
        }
    }, [filteredOffers, sortBy]);

    const toggleRetailerFilter = (retailerName: string) => {
        setFilters(prev => {
            const isSelected = prev.retailers.includes(retailerName);
            if (isSelected) {
                return {
                    ...prev,
                    retailers: prev.retailers.filter(r => r !== retailerName),
                };
            } else {
                return {
                    ...prev,
                    retailers: [...prev.retailers, retailerName],
                };
            }
        });
    };

    const clearAllFilters = () => {
        setFilters({
            stockStatus: 'all',
            retailers: [],
        });
    };

    const hasActiveFilters = filters.stockStatus !== 'all' || filters.retailers.length > 0;
    const activeFilterCount = (filters.stockStatus !== 'all' ? 1 : 0) + filters.retailers.length;

    const getStockStatusLabel = (status: string) => {
        switch (status) {
            case 'in_stock':
                return 'In Stock';
            case 'low_stock':
            case 'limited_stock':
                return 'Limited Stock';
            case 'out_of_stock':
                return 'Out of Stock';
            case 'pre_order':
                return 'Pre-Order';
            case 'discontinued':
                return 'Discontinued';
            default:
                return 'Unknown';
        }
    };

    const getStockStatusColor = (status: string) => {
        switch (status) {
            case 'in_stock':
                return 'bg-green-50 text-green-700 border-green-200';
            case 'low_stock':
            case 'limited_stock':
                return 'bg-amber-50 text-amber-700 border-amber-200';
            case 'out_of_stock':
                return 'bg-red-50 text-red-700 border-red-200';
            case 'pre_order':
                return 'bg-blue-50 text-blue-700 border-blue-200';
            case 'discontinued':
                return 'bg-gray-50 text-gray-600 border-gray-200';
            default:
                return 'bg-gray-50 text-gray-600 border-gray-200';
        }
    };

    return (
        <div className="bg-white rounded-lg shadow-sm border border-gray-100 overflow-hidden">
            <div className="p-6 border-b border-gray-100">
                <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
                    <div>
                        <h2 className="text-xl font-bold text-gray-900">Compare Prices</h2>
                        <p className="text-sm text-gray-600 mt-1">
                            {sortedOffers.length} of {retailerOffers.length} offers
                        </p>
                    </div>
                    
                    <div className="flex flex-wrap gap-3">
                        {/* Sort dropdown */}
                        <div className="relative">
                            <select
                                value={sortBy}
                                onChange={(e) => setSortBy(e.target.value as SortOption)}
                                className="appearance-none bg-white border border-gray-300 rounded-md py-2 pl-3 pr-10 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                aria-label="Sort options"
                            >
                                {Object.entries(sortOptions).map(([value, label]: [string, string]) => {
                                    if ((value !== 'recommended' || featureFlags.search.recommendedFilter) &&
                                        value !== 'cashback_desc' &&
                                        value !== 'cashback_asc') {
                                        return (
                                            <option key={value} value={value}>
                                                {label}
                                            </option>
                                        );
                                    }
                                    return null;
                                })}
                            </select>
                            <ArrowUpDown
                                size={16}
                                className="absolute right-3 top-2.5 text-gray-400 pointer-events-none"
                            />
                        </div>

                        {/* Filter button */}
                        <button
                            onClick={() => setShowFilters(!showFilters)}
                            className={`inline-flex items-center text-sm px-3 py-2 rounded-md border transition-colors ${
                                showFilters 
                                    ? 'bg-blue-50 border-blue-200 text-blue-700' 
                                    : 'border-gray-300 text-gray-700 hover:bg-gray-50'
                            }`}
                            aria-expanded={showFilters}
                            aria-controls="filters-panel"
                        >
                            <Filter size={16} className="mr-1.5" />
                            Filters
                            {activeFilterCount > 0 && (
                                <span className="ml-1.5 bg-blue-600 text-white rounded-full w-5 h-5 flex items-center justify-center text-xs">
                                    {activeFilterCount}
                                </span>
                            )}
                            <ChevronDown 
                                size={16} 
                                className={`ml-1 transition-transform ${showFilters ? 'rotate-180' : ''}`} 
                            />
                        </button>
                    </div>
                </div>

                {/* Filters panel */}
                {showFilters && (
                    <motion.div
                        id="filters-panel"
                        initial={{ opacity: 0, height: 0 }}
                        animate={{ opacity: 1, height: 'auto' }}
                        exit={{ opacity: 0, height: 0 }}
                        transition={{ duration: 0.2 }}
                        className="mt-4 pt-4 border-t border-gray-100"
                    >
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                            {/* Stock Status filter */}
                            <div>
                                <h3 className="font-medium text-gray-900 mb-3">Stock Status</h3>
                                <div className="space-y-2">
                                    {[
                                        { value: 'all', label: 'All Stock Levels', count: stockStatusCounts.all },
                                        { value: 'in_stock', label: 'In Stock', count: stockStatusCounts.in_stock },
                                        { value: 'low_stock', label: 'Limited Stock', count: stockStatusCounts.low_stock },
                                        { value: 'out_of_stock', label: 'Out of Stock', count: stockStatusCounts.out_of_stock },
                                        { value: 'pre_order', label: 'Pre-Order', count: stockStatusCounts.pre_order },
                                        { value: 'discontinued', label: 'Discontinued', count: stockStatusCounts.discontinued },
                                    ].map((option) => (
                                        option.count > 0 && (
                                            <label key={option.value} className="flex items-center group cursor-pointer">
                                                <input
                                                    type="radio"
                                                    name="stockStatus"
                                                    value={option.value}
                                                    checked={filters.stockStatus === option.value}
                                                    onChange={() => setFilters(prev => ({ ...prev, stockStatus: option.value as StockFilterOption }))}
                                                    className="rounded-full text-blue-600 focus:ring-blue-500 focus:ring-offset-0 mr-3"
                                                />
                                                <span className="text-sm text-gray-700 group-hover:text-gray-900 flex-1">
                                                    {option.label}
                                                </span>
                                                <span className="text-xs text-gray-500 bg-gray-100 rounded-full px-2 py-0.5 min-w-[24px] text-center">
                                                    {option.count}
                                                </span>
                                            </label>
                                        )
                                    ))}
                                </div>
                            </div>

                            {/* Retailers filter */}
                            <div>
                                <h3 className="font-medium text-gray-900 mb-3">Retailers</h3>
                                <div className="space-y-2 max-h-48 overflow-y-auto">
                                    {uniqueRetailers.map((retailer) => (
                                        <label key={retailer} className="flex items-center group cursor-pointer">
                                            <input
                                                type="checkbox"
                                                checked={filters.retailers.includes(retailer)}
                                                onChange={() => toggleRetailerFilter(retailer)}
                                                className="rounded text-blue-600 focus:ring-blue-500 focus:ring-offset-0 mr-3"
                                            />
                                            <span className="text-sm text-gray-700 group-hover:text-gray-900 flex-1">
                                                {retailer}
                                            </span>
                                        </label>
                                    ))}
                                </div>
                            </div>
                        </div>

                        <div className="mt-6 flex justify-between items-center">
                            <button
                                onClick={clearAllFilters}
                                className="text-sm text-blue-600 hover:text-blue-800 disabled:text-gray-400"
                                disabled={!hasActiveFilters}
                            >
                                Clear all filters
                            </button>
                            <button
                                onClick={() => setShowFilters(false)}
                                className="text-sm text-gray-600 hover:text-gray-800 md:hidden"
                            >
                                Close filters
                            </button>
                        </div>
                    </motion.div>
                )}
            </div>

            {/* Results */}
            <div className="divide-y divide-gray-100">
                {sortedOffers.length > 0 ? (
                    sortedOffers.map((offer, i) => (
                        <motion.div
                            key={`${offer.retailer.name}-${offer.price}-${i}`}
                            initial={{ opacity: 0, y: 10 }}
                            animate={{ opacity: 1, y: 0 }}
                            transition={{ delay: i * 0.05, duration: 0.2 }}
                            className="p-4 sm:p-6 hover:bg-gray-50 transition-colors"
                        >
                            <div className="flex flex-col sm:flex-row sm:items-center gap-4">
                                {/* Retailer info */}
                                <div className="flex-shrink-0 sm:w-48">
                                    <div className="flex items-center gap-4">
                                        <div className="h-16 w-16 rounded-lg bg-gray-50 flex items-center justify-center overflow-hidden p-2">
                                            {offer.retailer.logoUrl ? (
                                                <Image
                                                    src={offer.retailer.logoUrl}
                                                    alt={offer.retailer.name}
                                                    width={64}
                                                    height={64}
                                                    className="object-contain w-full h-full"
                                                    loading="lazy"
                                                    onError={(e) => {
                                                        const target = e.target as HTMLImageElement;
                                                        target.src = `https://placehold.co/600x600/f1f5f9/64748b.png?text=${encodeURIComponent(offer.retailer.name)}`;
                                                    }}
                                                />
                                            ) : (
                                                
                                                <div className="w-12 h-12 bg-background rounded-xl flex items-center justify-center border-2 border-border group-hover:border-primary/30 transition-colors">
                                                <Store className="h-6 w-6 text-muted-foreground group-hover:text-primary transition-colors" />
                                                </div>
                                            )}
                                        </div>
                                        <div>
                                            <h3 className="font-medium text-gray-900">{offer.retailer.name}</h3>
                                            <p className="text-sm text-gray-500">New</p>
                                        </div>
                                    </div>
                                </div>

                                {/* Price & status */}
                                <div className="flex-grow">
                                    <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-2">
                                        <div>
                                            <div className="text-2xl font-bold text-gray-900">
                                                {formatPrice(offer.price)}
                                            </div>
                                        </div>
                                        <div className="flex flex-col sm:items-end gap-1">
                                            <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium border ${getStockStatusColor(offer.stockStatus)}`}>
                                                {['in_stock', 'low_stock', 'limited_stock'].includes(offer.stockStatus) && (
                                                    <Check size={12} className="mr-1" />
                                                )}
                                                {getStockStatusLabel(offer.stockStatus)}
                                            </span>
                                            <p className="text-sm text-gray-500">
                                                Free delivery available
                                            </p>
                                        </div>
                                    </div>
                                </div>

                                {/* Action button */}
                                <div className="flex-shrink-0">
                                    <Link 
                                        href={offer.url} 
                                        target="_blank" 
                                        rel="noopener noreferrer"
                                        className="w-full sm:w-auto"
                                    >
                                        <motion.button
                                            whileHover={{ scale: 1.02 }}
                                            whileTap={{ scale: 0.98 }}
                                            className="w-full sm:w-auto inline-flex justify-center items-center px-6 py-2.5 bg-blue-600 hover:bg-blue-700 text-white font-medium rounded-md transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed"
                                            disabled={offer.stockStatus === 'out_of_stock' || offer.stockStatus === 'discontinued'}
                                        >
                                            <ShoppingCart size={18} className="mr-2" />
                                            Buy Now
                                        </motion.button>
                                    </Link>
                                </div>
                            </div>
                        </motion.div>
                    ))
                ) : (
                    <div className="p-12 text-center">
                        <div className="mx-auto w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mb-4">
                            <Filter className="h-8 w-8 text-gray-400" />
                        </div>
                        <h3 className="text-lg font-medium text-gray-900 mb-2">No offers match your filters</h3>
                        <p className="text-gray-500 mb-4">
                            Try adjusting your filter criteria to see more results.
                        </p>
                        {hasActiveFilters && (
                            <button
                                onClick={clearAllFilters}
                                className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-blue-600 bg-blue-50 hover:bg-blue-100 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
                            >
                                Clear all filters
                            </button>
                        )}
                    </div>
                )}
            </div>
        </div>
    );
}
