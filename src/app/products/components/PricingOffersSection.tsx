'use client';

import React, { useMemo } from 'react';
import { Store, PoundSterling } from 'lucide-react';
import Link from 'next/link';
import { Button } from '../../../components/ui/button';
import { formatPrice } from '@/lib/utils';
import { formatDate } from '@/app/utils/date';
import { TransformedProduct } from '@/lib/data/types';

interface PricingOffersSectionProps {
    product: TransformedProduct;
    onScrollToOffers: () => void;
}

export function PricingOffersSection({ product, onScrollToOffers }: PricingOffersSectionProps) {
    // Enhanced data calculations for pricing design
    const priceRange = useMemo(() => {
        if (!product.retailerOffers?.length) return null;
        
        const validPrices = product.retailerOffers
            .map((offer: any) => offer.price)
            .filter((price: any): price is number => typeof price === 'number' && price > 0);
            
        if (validPrices.length === 0) return null;
        
        const minPrice = Math.min(...validPrices);
        const maxPrice = Math.max(...validPrices);
        
        return { min: minPrice, max: maxPrice, count: validPrices.length };
    }, [product.retailerOffers]);

    const partnersText = useMemo(() => {
        if (!product.retailerOffers?.length) return "No partners available";
        
        const firstPartner = product.retailerOffers[0]?.retailer?.name;
        const totalPartners = product.retailerOffers.length;
        
        if (totalPartners === 1) {
            return `Available at ${firstPartner}`;
        } else {
            return `Available at ${firstPartner} and ${totalPartners - 1} other verified partner${totalPartners > 2 ? 's' : ''}`;
        }
    }, [product.retailerOffers]);

    const bestOffer = useMemo(() => {
        if (!product.retailerOffers?.length) return null;
        
        const validOffers = product.retailerOffers
            .filter((offer: any) => typeof offer.price === 'number' && offer.price > 0);
        
        if (validOffers.length === 0) return null;
        
        return validOffers.reduce((best: any, current: any) => {
            return current.price < best.price ? current : best;
        });
    }, [product.retailerOffers]);

    return (
        <div id="pricing-offers" className="space-y-4 mb-8">
            {/* Availability Section - Light Blue Background */}
            {priceRange && (
                <div className="bg-blue-50 border border-blue-200 rounded-2xl p-3">
                    <div className="flex items-center gap-2">
                        <Store className="h-4 w-4 text-gray-600 flex-shrink-0" />
                        <p className="text-gray-800 text-sm">
                            {partnersText} from: {formatPrice(priceRange.min)}
                            {priceRange.min !== priceRange.max && (
                                <> - {formatPrice(priceRange.max)}</>
                            )} - <button 
                                onClick={onScrollToOffers}
                                className="text-blue-600 hover:text-blue-800 underline font-medium"
                            >
                                See More
                            </button>.
                        </p>
                    </div>
                </div>
            )}

            {/* Your Savings Section - Yellow/Cream Background */}
            {product.cashbackAmount && product.cashbackAmount > 0 && bestOffer && (
                <div className="bg-yellow-50 border border-yellow-200 rounded-2xl p-4">
                    <div className="flex items-center gap-2 mb-3">
                        <PoundSterling className="h-4 w-4 text-yellow-700 flex-shrink-0" />
                        <h3 className="text-base font-bold text-gray-800">Your Savings</h3>
                    </div>
                    
                    <div className="space-y-2">
                        <div className="flex justify-between items-center">
                            <span className="text-gray-800 text-sm">{bestOffer.retailer?.name} Price:</span>
                            <span className="text-sm font-bold text-gray-800">{formatPrice(bestOffer.price)}</span>
                        </div>
                        
                        <div className="flex justify-between items-center">
                            <span className="text-gray-800 text-sm">Cashback from {product.brand?.name || 'Brand'}:</span>
                            <span className="text-sm font-bold text-green-600">-{formatPrice(product.cashbackAmount)}</span>
                        </div>
                        
                        <hr className="border-yellow-300" />
                        
                        <div className="flex justify-between items-center">
                            <span className="text-sm font-bold text-gray-900">Price After Cashback</span>
                            <span className="text-base font-bold text-green-600">
                                {formatPrice((bestOffer.price ?? 0) - (product.cashbackAmount ?? 0))}
                            </span>
                        </div>
                    </div>
                </div>
            )}

            {/* Dates Section - Simple Layout */}
            {product.promotion?.purchaseEndDate && (
                <div className="grid grid-cols-2 gap-4 text-center">
                    <div>
                        <div className="text-lg font-bold text-gray-900 mb-1">Buy by:</div>
                        <div className="text-base text-gray-700">
                            {formatDate(product.promotion.purchaseEndDate)}
                        </div>
                    </div>
                    {product.promotion.claimWindowDays && (
                        <div>
                            <div className="text-lg font-bold text-gray-900 mb-1">Claim by:</div>
                            <div className="text-base text-gray-700">
                                {(() => {
                                    const claimStartOffset = product.promotion.claimStartOffsetDays || 0;
                                    const purchaseEndDate = new Date(product.promotion.purchaseEndDate);
                                    const claimEndDate = new Date(purchaseEndDate);
                                    claimEndDate.setDate(claimEndDate.getDate() + claimStartOffset + product.promotion.claimWindowDays);
                                    return formatDate(claimEndDate.toISOString());
                                })()}
                            </div>
                        </div>
                    )}
                </div>
            )}

            {/* Action Buttons - Simple Blue and Gray */}
            <div className="space-y-3">
                {product.cashbackAmount && product.cashbackAmount > 0 && bestOffer?.url && (
                    <Link 
                        href={bestOffer.url} 
                        target="_blank" 
                        rel="noopener noreferrer"
                        className="block"
                    >
                        <Button className="w-full bg-blue-500 hover:bg-blue-600 text-white text-lg font-bold py-4 rounded-full">
                            Save {formatPrice(product.cashbackAmount)} Now
                        </Button>
                    </Link>
                )}
                
                <Button 
                    variant="secondary"
                    className="w-full bg-gray-200 hover:bg-gray-300 text-gray-900 text-lg font-bold py-4 rounded-full"
                    onClick={onScrollToOffers}
                >
                    <u>See All Prices</u>
                </Button>
            </div>

            {/* Footer - Simple Text */}
            <div className="text-center text-gray-500 text-sm">
                Partner merchants • Receipt-based rebate
            </div>
        </div>
    );
}