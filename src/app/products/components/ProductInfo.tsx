'use client';

import React, { useState, useRef, useEffect, useMemo, useCallback } from 'react';
import { motion } from 'framer-motion';
import { Tag, Store, Clock, ChevronLeft, ChevronRight } from 'lucide-react';
import Link from 'next/link';
import { ResilientImage } from '@/components/ui/ResilientImage';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from "../../../components/ui/dialog";
import { Button } from "../../../components/ui/button";
import { formatPrice } from "../../../lib/utils";
import { formatDate } from '@/app/utils/date';
import { PricingOffersSection } from '@/app/products/components/PricingOffersSection';
import { TransformedProduct } from '@/lib/data/types';
// Removed ProductStructuredData import - handled at page level to prevent duplication

interface ProductInfoProps {
    product: TransformedProduct;
}

export function ProductInfo({ product }: ProductInfoProps) {
    const transformedProduct = product;
    const [isClaimDetailsExpanded, setIsClaimDetailsExpanded] = useState(false);
    const contentRef = useRef<HTMLDivElement>(null);

    const toggleClaimDetails = () => {
        setIsClaimDetailsExpanded(!isClaimDetailsExpanded);
    };


    const [currentImageIndex, setCurrentImageIndex] = useState(0);

    // Memoized image URL processing to prevent function recreation on every render
    const getImageUrl = useCallback((image: string) => {
        if (!image) {
            return transformedProduct.brand?.logoUrl || 
                `https://placehold.co/600x600/f1f5f9/64748b.png?text=${encodeURIComponent(transformedProduct.name)}`;
        }

        // Define common invalid image path patterns
        const invalidPatterns = [
            'URL_to_image_1',
            'image_url_1.jpg',
            'example.com',
            'blank.html',
            'placeholder-product.png'
        ];

        // Check if the imagePath is invalid
        if (invalidPatterns.some(pattern => image.includes(pattern))) {
            return transformedProduct.brand?.logoUrl || 
                `https://placehold.co/600x600/f1f5f9/64748b.png?text=${encodeURIComponent(transformedProduct.name)}`;
        }

        if (image.startsWith('http')) {
            return image;
        }

        const fullUrl = `${process.env.NEXT_PUBLIC_SUPABASE_URL}/storage/v1/object/public/${image}`;
        return fullUrl;
    }, [transformedProduct.brand?.logoUrl, transformedProduct.name]);


    // Memoized image state to prevent array processing on every render
    const productImages = useMemo(() => transformedProduct.images || [], [transformedProduct.images]);
    const hasMultipleImages = useMemo(() => productImages.length > 1, [productImages.length]);
    
    const currentImage = useMemo(() => {
        return productImages.length > 0 
            ? productImages[currentImageIndex]
            : (transformedProduct.brand?.logoUrl || `https://placehold.co/600x600/f1f5f9/64748b.png?text=${encodeURIComponent(transformedProduct.name)}`);
    }, [productImages, currentImageIndex, transformedProduct.brand?.logoUrl, transformedProduct.name]);

    // Memoized navigation functions to prevent recreation on every render
    const nextImage = useCallback(() => {
        if (productImages.length > 0) {
            setCurrentImageIndex((prev) => (prev + 1) % productImages.length);
        }
    }, [productImages.length]);

    const previousImage = useCallback(() => {
        if (productImages.length > 0) {
            setCurrentImageIndex((prev) => (prev - 1 + productImages.length) % productImages.length);
        }
    }, [productImages.length]);

    // Memoized thumbnail click handler to prevent function recreation
    const handleThumbnailClick = useCallback((index: number) => {
        setCurrentImageIndex(index);
    }, []);

    // Memoized date calculation to prevent creating new Date objects on every render
    const daysUntilEnd = useMemo(() => {
        if (!transformedProduct.promotion?.purchaseEndDate) return 0;
        
        const endDate = new Date(transformedProduct.promotion.purchaseEndDate);
        const now = new Date();
        return Math.ceil((endDate.getTime() - now.getTime()) / (1000 * 60 * 60 * 24));
    }, [transformedProduct.promotion?.purchaseEndDate]);



    return (
        <>
            {/* ProductStructuredData removed to prevent duplication - handled at page level */}
            
            <motion.div
                initial={{ y: 20, opacity: 0 }}
                animate={{ y: 0, opacity: 1 }}
                className="grid md:grid-cols-2 gap-8 mb-12"
            >
                <div className="space-y-4">
                    <div className="aspect-square bg-secondary/10 rounded-lg flex items-center justify-center overflow-hidden relative">
                        <ResilientImage
                            src={getImageUrl(currentImage)}
                            alt={transformedProduct.name}
                            width={600} // Placeholder width
                            height={600} // Placeholder height
                            sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
                            className="object-contain"
                            onError={(error) => {
                                console.warn(`Product main image error for ${transformedProduct.name}:`, error);
                                // Note: handleImageError expects a React event, but ResilientImage onError provides a string
                                // We'll just log the error here since ResilientImage handles fallbacks internally
                            }}
                            priority
                            productName={transformedProduct.name}
                            brandName={transformedProduct.brand?.name}
                            enableValidation={true}
                            showLoadingState={true}
                            retryOnError={true}
                        />
                        
                        {hasMultipleImages && (
                            <>
                                <Button
                                    variant="secondary"
                                    size="icon"
                                    className="absolute left-2 top-1/2 -translate-y-1/2 z-10"
                                    onClick={previousImage}
                                    aria-label="Previous image"
                                >
                                    <ChevronLeft className="h-4 w-4" />
                                </Button>
                                <Button
                                    variant="secondary"
                                    size="icon"
                                    className="absolute right-2 top-1/2 -translate-y-1/2 z-10"
                                    onClick={nextImage}
                                    aria-label="Next image"
                                >
                                    <ChevronRight className="h-4 w-4" />
                                </Button>
                            </>
                        )}

                        <div className="absolute top-4 right-4 bg-secondary text-white px-4 py-2 rounded-lg font-medium">
                            {(transformedProduct.cashbackAmount ?? 0) > 0
                                ? `Claim ${formatPrice(transformedProduct.cashbackAmount!)} cashback`
                                : 'No Cashback Available'
                            }
                        </div>
                    </div>

                    {hasMultipleImages && (
                        <div className="flex justify-center gap-2 py-10">
                            <div className="flex gap-2 overflow-x-auto max-w-full px-2">
                                {productImages.map((image: string, index: number) => (
                                    <button
                                        key={index}
                                        onClick={() => handleThumbnailClick(index)}
                                        className={`relative w-20 h-20 rounded-lg overflow-hidden flex-shrink-0 
                                            ${index === currentImageIndex ? 'ring-2 ring-primary' : ''}`}
                                        aria-label={`View image ${index + 1} of ${productImages.length}`}
                                    >
                                        <ResilientImage
                                            src={getImageUrl(image)}
                                            alt={`${transformedProduct.name} - View ${index + 1}`}
                                            width={80} // Placeholder width
                                            height={80} // Placeholder height
                                            sizes="80px"
                                            className="object-cover"
                                            priority={index === 0}
                                            onError={(error) => {
                                                console.warn(`Product thumbnail error for ${transformedProduct.name} (image ${index + 1}):`, error);
                                                // Note: ResilientImage handles fallbacks internally, no need to call handleImageError
                                            }}
                                            productName={transformedProduct.name}
                                            brandName={transformedProduct.brand?.name}
                                            enableValidation={true}
                                            showLoadingState={false} // Don't show loading state for thumbnails
                                            retryOnError={false} // Don't retry thumbnails to avoid UI jank
                                        />
                                    </button>
                                ))}
                            </div>
                        </div>
                    )}
                </div>

                <div>
                    <h1 className="text-3xl font-bold text-primary mb-2">{transformedProduct.name}</h1>
                    {transformedProduct.brand && (
                        <Link href={`/brands/${transformedProduct.brand.id}`} className="inline-block">
                            <p className="text-xl text-foreground/70 mb-4 hover:text-primary transition-colors">
                                {transformedProduct.brand.name}
                            </p>
                        </Link>
                    )}
                    <div className="flex flex-wrap gap-4 mb-6">
                        {transformedProduct.category && (
                            <div className="flex items-center gap-2 text-sm text-foreground/70">
                                <Tag className="h-4 w-4 text-primary" />
                                <span>{transformedProduct.category.name}</span>
                            </div>
                        )}

                        <div className="flex items-center gap-2 text-sm text-foreground/70">
                            <Store className="h-4 w-4 text-primary" />
                            <span>{
                                (transformedProduct.retailerOffers?.length || 0) > 0 
                                    ? `${transformedProduct.retailerOffers.length} Retailers offering cashback`
                                    : 'No retailers available'
                            }</span>
                        </div>
                        {daysUntilEnd !== null && daysUntilEnd > 0 && (
                        <div className="flex items-center gap-2 text-sm text-foreground/70">
                            <Clock className="h-4 w-4 text-primary" />
                            <span>Ends in {daysUntilEnd} days</span>
                        </div>
                    )}
                    </div>

                    {/* Pricing & Offers Section - Now using extracted component for A/B testing */}
                    <PricingOffersSection 
                        product={transformedProduct}
                        onScrollToOffers={() => {
                            document.getElementById('retailer-offers')?.scrollIntoView({ behavior: 'smooth' });
                        }}
                    />

                    {/* Expandable Details - Keep existing functionality */}
                    {transformedProduct.promotion && (
                        <div className="mt-4">
                            <div className="text-center">
                                <button 
                                    onClick={toggleClaimDetails}
                                    className="text-primary hover:text-primary/80 underline text-sm"
                                >
                                    {isClaimDetailsExpanded ? 'Show less' : 'Show more'} claim details
                                </button>
                            </div>

                            <div 
                                className={`overflow-hidden transition-all duration-500 ease-in-out ${
                                    isClaimDetailsExpanded ? 'max-h-[400px] opacity-100' : 'max-h-0 opacity-0'
                                }`}
                            >
                                <div 
                                    ref={contentRef}
                                    className="mt-4 p-4 bg-muted rounded-xl border"
                                >
                                    <h4 className="font-bold text-foreground mb-3">Claim Process Details</h4>
                                    <div className="space-y-2 text-sm">
                                        <div className="flex justify-between">
                                            <span>Purchase by:</span>
                                            <span className="font-medium">
                                                {transformedProduct.promotion.purchaseEndDate ? 
                                                    formatDate(transformedProduct.promotion.purchaseEndDate) : 
                                                    'Not specified'}
                                            </span>
                                        </div>
                                        <div className="flex justify-between">
                                            <span>Claim starts:</span>
                                            <span className="font-medium">
                                                {transformedProduct.promotion.claimStartOffsetDays != null ? 
                                                    `${transformedProduct.promotion.claimStartOffsetDays} days after purchase` : 
                                                    'Immediately after purchase'}
                                            </span>
                                        </div>
                                        <div className="flex justify-between">
                                            <span>Claim window:</span>
                                            <span className="font-medium">
                                                {transformedProduct.promotion.claimWindowDays != null ? 
                                                    `${transformedProduct.promotion.claimWindowDays} days` : 
                                                    'Check terms for details'}
                                            </span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    )}

                    {/* Terms & Conditions Dialog - Keep existing functionality */}
                    {transformedProduct.promotion?.termsUrl && (
                        <div className="text-center mt-4">
                            <Dialog>
                                <DialogTrigger asChild>
                                    <button className="text-sm text-muted-foreground hover:text-primary underline">
                                        View {transformedProduct.promotion.title || 'Cashback'} Terms & Conditions
                                    </button>
                                </DialogTrigger>
                                <DialogContent>
                                    <DialogHeader>
                                        <DialogTitle>Cashback Terms & Conditions</DialogTitle>
                                        <DialogDescription className="space-y-2">
                                            {transformedProduct.promotion.termsDescription || 'Please review the full terms and conditions for this cashback offer.'}
                                            {transformedProduct.promotion.termsUrl && (
                                                <Link
                                                    href={transformedProduct.promotion.termsUrl}
                                                    target="_blank"
                                                    rel="noopener noreferrer"
                                                    className="inline-block mt-3 px-4 py-2 bg-primary text-primary-foreground rounded-md hover:bg-primary/90 transition-colors text-sm font-medium"
                                                >
                                                    View Full Terms & Conditions
                                                </Link>
                                            )}
                                        </DialogDescription>
                                    </DialogHeader>
                                </DialogContent>
                            </Dialog>
                        </div>
                    )}

                    {/* No Cashback Available State - Enhanced design with better spacing */}
                    {(!transformedProduct.cashbackAmount || transformedProduct.cashbackAmount <= 0) && (
                        <div className="bg-card border-2 border-border rounded-2xl shadow-lg p-10 text-center mb-10">
                            <div className="space-y-6">
                                <div className="w-20 h-20 bg-muted/50 rounded-2xl flex items-center justify-center mx-auto shadow-sm border border-border">
                                    <Store className="h-10 w-10 text-muted-foreground" />
                                </div>
                                <div className="space-y-3">
                                    <p className="text-xl font-bold text-foreground">No cashback currently available</p>
                                    <p className="text-base text-muted-foreground">This product doesn&apos;t have an active cashback promotion</p>
                                </div>
                            </div>
                        </div>
                    )}

                </div>
            </motion.div>
        </>
    );
}