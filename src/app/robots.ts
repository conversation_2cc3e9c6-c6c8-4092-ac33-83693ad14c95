// src/app/robots.ts
// This file generates the robots.txt file for the website

import { MetadataRoute } from 'next';
import { SITE_URL } from '@/config/domains';

export default function robots(): MetadataRoute.Robots {
  return {
    rules: [
      // Core search bots allowed
      { userAgent: 'Googlebot', allow: '/' },
      { userAgent: 'bingbot', allow: '/' },

      // Allow Google's AI extension
      { userAgent: 'Google-Extended', allow: '/' },

      // Block third-party AI training bots
      { userAgent: 'GPTBot', disallow: '/' },
      { userAgent: 'CCBot', disallow: '/' },
      { userAgent: 'PerplexityBot', disallow: '/' },
      { userAgent: 'Claude-Web', disallow: '/' },
      { userAgent: 'Applebot-Extended', disallow: '/' },
      { userAgent: 'Bytespider', disallow: '/' },

      // Default rule for all others
      { userAgent: '*', allow: '/', disallow: ['/api/', '/admin/'] },
    ],
    sitemap: `${SITE_URL}/sitemap.xml`,
  };
}