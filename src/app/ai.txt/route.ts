import { NextResponse } from 'next/server'

/**
 * AI Policy declaration (ai.txt)
 *
 * Balanced Approach: Allow core search bots (Googlebot) and their associated AI bots (Google-Extended),
 * but block third-party AI training bots from scraping data wholesale.
 *
 * This endpoint serves plaintext at /ai.txt aligned with site policy.
 */
export async function GET() {
  const lines = [
    '# ai.txt - AI crawler access policy',
    'User-agent: Google-Extended',
    'Allow: /',
    '',
    'User-agent: GPTBot',
    'Disallow: /',
    '',
    'User-agent: CCBot',
    'Disallow: /',
    '',
    'User-agent: PerplexityBot',
    'Disallow: /',
    '',
    'User-agent: <PERSON>-<PERSON>',
    'Disallow: /',
    '',
    'User-agent: Applebot-Extended',
    'Disallow: /',
    '',
    'User-agent: Bytespider',
    'Disallow: /'
  ]

  return new NextResponse(lines.join('\n'), {
    status: 200,
    headers: {
      'Content-Type': 'text/plain; charset=utf-8',
      'Cache-Control': 'public, max-age=3600'
    }
  })
}

