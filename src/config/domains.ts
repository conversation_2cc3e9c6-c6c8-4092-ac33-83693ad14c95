/**
 * =================================================================================
 * DOMAIN CONFIGURATION
 *
 * Centralized configuration for all domain and URL-related constants.
 * This file is the single source of truth for domain management across the app.
 *
 * - `SITE_URL`: The canonical URL for the current environment.
 * - `PRODUCTION_DOMAINS`: Hardcoded production domains for various services.
 * - `CORS_ORIGINS`: Allowed origins for CORS policies.
 * =================================================================================
 */

/**
 * Determines the canonical site URL based on the environment.
 * - In development, it defaults to localhost with flexible port detection.
 * - In production/staging, it uses AWS Amplify environment variables with branch-aware fallback.
 * - Works across all AWS Amplify branches without requiring env.mjs dependency.
 */
const getSiteUrl = (): string => {
  // Debug environment variables in non-development environments
  if (process.env.NODE_ENV !== 'development') {
    console.log('🔍 Environment Debug:', {
      AWS_BRANCH: process.env.AWS_BRANCH || '[NOT_SET]',
      NEXT_PUBLIC_SITE_URL: process.env.NEXT_PUBLIC_SITE_URL ? `[SET: ${process.env.NEXT_PUBLIC_SITE_URL}]` : '[MISSING]',
      NEXT_PUBLIC_SUPABASE_URL: process.env.NEXT_PUBLIC_SUPABASE_URL ? '[SET]' : '[MISSING]',
      NODE_ENV: process.env.NODE_ENV
    });
  }

  // First priority: Explicit AWS Amplify environment variables
  const runtimeSiteUrl = process.env.NEXT_PUBLIC_SITE_URL || process.env.NEXT_PUBLIC_APP_URL;
  
  // AWS Amplify branch-aware URL override
  // This handles the case where NEXT_PUBLIC_SITE_URL is set to main for all branches
  const awsBranch = process.env.AWS_BRANCH;
  if (runtimeSiteUrl && awsBranch) {
    // If the URL points to main but we're on a different branch, use branch-specific URL
    if (runtimeSiteUrl.includes('main.d3pcuskj59hcq9') && awsBranch !== 'main') {
      const branchUrls: Record<string, string> = {
        'buildopt': 'https://buildopt.d3pcuskj59hcq9.amplifyapp.com',
        'staging': 'https://staging.d3pcuskj59hcq9.amplifyapp.com',
        // Add other branches as needed
      };
      
      const branchUrl = branchUrls[awsBranch];
      if (branchUrl) {
        console.log(`🔄 Branch override: ${awsBranch} → ${branchUrl}`);
        return branchUrl;
      }
    }
  }
  
  // Use the environment variable if available and valid
  if (runtimeSiteUrl && runtimeSiteUrl.trim() !== '') {
    return runtimeSiteUrl.replace(/\/$/, ''); // Remove trailing slash
  }

  // Development localhost with flexible port detection (preserves existing functionality)
  if (process.env.NODE_ENV === 'development') {
    // In development, use window.location for client-side consistency
    if (typeof window !== 'undefined') {
      const clientUrl = `${window.location.protocol}//${window.location.host}`;
      console.log(`🏠 Development URL (client): ${clientUrl}`);
      return clientUrl;
    }
    
    // Server-side fallback
    const devUrl = `http://localhost:${process.env.PORT || 3000}`;
    console.log(`🏠 Development URL (server): ${devUrl}`);
    return devUrl;
  }
  
  // AWS Amplify branch-based fallback when no explicit URL is set
  if (awsBranch) {
    const branchUrls: Record<string, string> = {
      'main': 'https://main.d3pcuskj59hcq9.amplifyapp.com',
      'buildopt': 'https://buildopt.d3pcuskj59hcq9.amplifyapp.com',
      'staging': 'https://staging.d3pcuskj59hcq9.amplifyapp.com',
    };
    
    const branchUrl = branchUrls[awsBranch];
    if (branchUrl) {
      console.log(`🌿 Branch fallback: ${awsBranch} → ${branchUrl}`);
      return branchUrl;
    }
  }
  
  // Final safety fallback to production domain
  console.log('⚠️ Using final fallback URL');
  return PRODUCTION_DOMAINS.AWS_AMPLIFY.MAIN;
};

/**
 * The canonical URL for the current environment.
 * Used for sitemaps, structured data, and other absolute URL generation.
 */
export const SITE_URL = getSiteUrl();

/**
 * A collection of hardcoded production domains.
 * These are stable and used for security policies like CORS.
 */
export const PRODUCTION_DOMAINS = {
  AWS_AMPLIFY: {
    CURRENT: 'https://buildopt.d3pcuskj59hcq9.amplifyapp.com/',
    MAIN: 'https://main.d3pcuskj59hcq9.amplifyapp.com',
    STAGING: 'https://buildopt.d3pcuskj59hcq9.amplifyapp.com/',
  },
  CUSTOM: 'https://cashbackdeals.com'
};

/**
 * Allowed origins for Cross-Origin Resource Sharing (CORS).
 * Combines custom domains and Amplify preview domains.
 */
export const CORS_ORIGINS: (string | RegExp)[] = [
  PRODUCTION_DOMAINS.CUSTOM,
  `www.${PRODUCTION_DOMAINS.CUSTOM}`,
  // Also allow development origins
  ...(process.env.NODE_ENV === 'development'
    ? [
        'http://localhost:3003',
        'http://127.0.0.1:3003',
        /^http:\/\/localhost:\d+$/,
        /^http:\/\/127\.0\.0\.1:\d+$/
      ]
    : [])
];

/**
 * Returns the primary canonical domain for the application.
 * Used as a fallback for non-browser requests.
 */
export const getCanonicalDomain = (): string => {
  return PRODUCTION_DOMAINS.CUSTOM;
};
