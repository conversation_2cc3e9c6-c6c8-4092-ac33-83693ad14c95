import { Metadata } from 'next';
import { SITE_URL } from '@/config/domains';

// Base metadata object that will be used as default for all pages
export const siteConfig = {
  name: 'RebateRay',
  description: 'Discover and compare cashback deals and rebates from top brands in the UK.',
  url: SITE_URL,
};

// Helper function to ensure image URLs are absolute and accessible to external services
function getAbsoluteImageUrl(image: string): string {
  if (!image) return '';
  
  // If it's already a full URL, return it as is
  if (image.startsWith('http')) {
    return image;
  }
  
  // Construct the Supabase storage URL for relative paths
  return `${process.env.NEXT_PUBLIC_SUPABASE_URL}/storage/v1/object/public/${image}`;
}

// Enhanced metadata construction options
export interface MetadataOptions {
  title?: string;
  description?: string;
  image?: string;
  noIndex?: boolean;
  pathname?: string;
  openGraph?: Record<string, any>;
  keywords?: string[];
  pageType?: 'website' | 'article' | 'profile';
  structuredData?: Record<string, any>;
}

// Helper function to construct metadata for any page
export function constructMetadata({
  title,
  description,
  image,
  noIndex = false,
  pathname,
  openGraph,
  keywords = [],
  pageType = 'website',
  structuredData,
}: MetadataOptions): Metadata {
  const metaTitle = title 
    ? `${title} | ${siteConfig.name}` 
    : `${siteConfig.name} - Find the Best Rebates and Cashback Reward Deals`;
  
  const metaDescription = description || siteConfig.description;
  
  // Construct canonical URL
  const url = pathname 
    ? `${siteConfig.url}${pathname}` 
    : siteConfig.url;
  
  // Ensure image URL is absolute for social media crawlers
  const absoluteImageUrl = image ? getAbsoluteImageUrl(image) : undefined;

  const metadata: Metadata = {
    title: metaTitle,
    description: metaDescription,
    metadataBase: new URL(siteConfig.url),
    openGraph: {
      title: metaTitle,
      description: metaDescription,
      url,
      siteName: siteConfig.name,
      images: absoluteImageUrl ? [{ url: absoluteImageUrl }] : undefined,
      type: pageType,
      ...(openGraph || {}),
    } as any,
    twitter: {
      card: 'summary_large_image',
      title: metaTitle,
      description: metaDescription,
      images: absoluteImageUrl ? [absoluteImageUrl] : undefined,
    },
    robots: {
      index: !noIndex,
      follow: !noIndex,
    },
    alternates: {
      canonical: url,
    },
  };

  // Add keywords if provided
  if (keywords.length > 0) {
    metadata.keywords = keywords;
  }

  // Add structured data if provided
  if (structuredData) {
    metadata.other = {
      'script:ld+json': JSON.stringify(structuredData),
    };
  }

  return metadata;
}

// Error metadata for not found or error pages
export function createErrorMetadata(
  title: string = 'Page Not Found',
  description: string = 'The requested page could not be found.',
  statusCode: number = 404
): Metadata {
  return constructMetadata({
    title,
    description,
    noIndex: true,
    openGraph: {
      type: 'website',
    },
  });
}

// UUID validation utility
export function isValidUUID(id: string): boolean {
  return /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i.test(id);
}

// Standardized error handling for page components
export async function handlePageError<T>(
  operation: () => Promise<T>,
  errorTitle: string = 'Page Not Found',
  errorDescription: string = 'The requested page could not be found.'
): Promise<{ data: T | null; metadata: Metadata | null }> {
  try {
    const data = await operation();
    return { data, metadata: null };
  } catch (error) {
    console.error(`Page error: ${errorTitle}`, error);
    return {
      data: null,
      metadata: createErrorMetadata(errorTitle, errorDescription),
    };
  }
}

// Brand-specific metadata helper
export function createBrandMetadata(brand: {
  name: string;
  description?: string | null;
  logoUrl?: string | null;
  slug?: string | null;
  id: string;
}): Metadata {
  const title = `${brand.name} Promotions & Cashback Deals`;
  const description = brand.description || `Find the latest ${brand.name} cashback offers and promotions.`;
  const pathname = `/brands/${brand.slug || brand.id}`;

  return constructMetadata({
    title,
    description,
    image: brand.logoUrl || undefined,
    pathname,
    keywords: [brand.name, 'cashback', 'deals', 'promotions', 'offers'],
    pageType: 'website',
  });
}

// Product-specific metadata helper
export function createProductMetadata(product: {
  name: string;
  description?: string | null;
  images?: string[] | null;
  brand?: { name: string } | null;
  slug?: string | null;
  id: string;
}): Metadata {
  const title = `${product.name} - Best Cashback Deals`;
  const description = product.description
    ? `${product.description.substring(0, 155)}...`
    : `Get cashback on ${product.name} from ${product.brand?.name || 'top retailers'}. Compare prices and save money with our exclusive offers.`;
  
  const primaryImage = product.images && product.images.length > 0
    ? product.images[0]
    : (product.brand as any)?.logoUrl || undefined;

  const pathname = `/products/${product.slug || product.id}`;

  return constructMetadata({
    title,
    description,
    image: primaryImage,
    pathname,
    keywords: [product.name, (product.brand as any)?.name, 'cashback', 'deals', 'offers'].filter((k): k is string => Boolean(k)),
    pageType: 'website',
  });
}

// Retailer-specific metadata helper
export function createRetailerMetadata(retailer: {
  name: string;
  description?: string | null;
  logoUrl?: string | null;
  slug?: string | null;
  id: string;
}): Metadata {
  const title = `${retailer.name} Cashback Deals & Offers`;
  const description = retailer.description
    ? `${retailer.description.substring(0, 155)}...`
    : `Shop at ${retailer.name} and earn cashback on your purchases. Discover exclusive cashback deals and guaranteed rewards.`;
  
  const pathname = `/retailers/${retailer.slug || retailer.id}`;

  return constructMetadata({
    title,
    description,
    image: retailer.logoUrl || undefined,
    pathname,
    keywords: [retailer.name, 'cashback', 'deals', 'offers', 'discounts', 'save money', 'rewards', 'rebates'],
    pageType: 'website',
  });
}
