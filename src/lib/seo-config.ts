// SEO Configuration Objects and Templates
// Centralized SEO patterns and reusable configurations

import { SITE_URL } from '@/config/domains';

// Page type definitions for consistent SEO handling
export type PageType = 'brand' | 'product' | 'retailer' | 'listing' | 'search' | 'home';

// Structured data templates
export const structuredDataTemplates = {
  // Brand/Organization schema template
  brand: (data: {
    id: string;
    name: string;
    description?: string | null;
    logoUrl?: string | null;
    websiteUrl?: string | null;
    canonicalUrl?: string;
  }) => ({
    '@context': 'https://schema.org',
    '@type': 'Brand',
    name: data.name,
    description: data.description,
    logo: data.logoUrl,
    url: data.websiteUrl || data.canonicalUrl,
    identifier: data.id,
  }),

  // Product schema template
  product: (data: {
    id: string;
    name: string;
    description?: string | null;
    images?: string[];
    brand?: { name: string; logoUrl?: string | null };
    category?: string;
    modelNumber?: string;
    slug?: string;
    offers?: any[];
  }) => ({
    '@context': 'https://schema.org',
    '@type': 'Product',
    name: data.name,
    description: data.description || `${data.name} with cashback offer`,
    image: data.images && data.images.length > 0 ? data.images : undefined,
    sku: data.modelNumber || data.id,
    mpn: data.modelNumber,
    url: `${SITE_URL}/products/${data.slug || data.id}`,
    brand: data.brand ? {
      '@type': 'Brand',
      name: data.brand.name,
      logo: data.brand.logoUrl,
    } : undefined,
    category: data.category,
    offers: data.offers,
  }),

  // Organization schema template (for retailers)
  organization: (data: {
    id: string;
    name: string;
    description?: string | null;
    logoUrl?: string | null;
    websiteUrl?: string | null;
  }) => ({
    '@context': 'https://schema.org',
    '@type': 'Organization',
    name: data.name,
    description: data.description,
    logo: data.logoUrl,
    url: data.websiteUrl,
    identifier: data.id,
  }),

  // BreadcrumbList schema template
  breadcrumbList: (items: Array<{ name: string; url: string }>) => ({
    '@context': 'https://schema.org',
    '@type': 'BreadcrumbList',
    itemListElement: items.map((item, index) => ({
      '@type': 'ListItem',
      position: index + 1,
      name: item.name,
      item: {
        '@type': 'Thing',
        '@id': item.url.startsWith('http') ? item.url : `${SITE_URL}${item.url}`,
        name: item.name,
      },
    })),
  }),

  // CollectionPage schema template (for listing pages)
  collectionPage: (data: {
    name: string;
    description: string;
    url: string;
    itemCount: number;
    itemType: 'Brand' | 'Product' | 'Organization';
  }) => ({
    '@context': 'https://schema.org',
    '@type': 'CollectionPage',
    name: data.name,
    description: data.description,
    url: data.url,
    mainEntity: {
      '@type': 'ItemList',
      numberOfItems: data.itemCount,
      itemListElement: {
        '@type': 'ListItem',
        position: 1,
        item: {
          '@type': 'ItemList',
          name: `${data.itemType} Collection`,
        },
      },
    },
  }),
};

// OpenGraph configuration templates
export const openGraphTemplates = {
  // Standard page OpenGraph
  standard: {
    type: 'website' as const,
    locale: 'en_GB',
  },

  // Product page OpenGraph
  product: {
    type: 'website' as const, // Note: Next.js doesn't support og:type="product"
    locale: 'en_GB',
  },

  // Brand page OpenGraph
  brand: {
    type: 'website' as const,
    locale: 'en_GB',
  },

  // Listing page OpenGraph
  listing: {
    type: 'website' as const,
    locale: 'en_GB',
  },
};

// Keywords templates by page type
export const keywordTemplates = {
  brand: (brandName: string) => [
    brandName,
    'cashback',
    'deals',
    'promotions',
    'offers',
    `${brandName} cashback`,
    'save money',
    'rewards',
  ],

  product: (productName: string, brandName?: string) => [
    productName,
    brandName,
    'cashback',
    'deals',
    'offers',
    'best price',
    'compare prices',
    'save money',
  ].filter(Boolean) as string[],

  retailer: (retailerName: string) => [
    retailerName,
    'cashback',
    'deals',
    'offers',
    'discounts',
    `${retailerName} cashback`,
    'save money',
    'rewards',
    'rebates',
  ],

  listing: (type: string) => [
    type,
    'cashback',
    'deals',
    'offers',
    'compare',
    'save money',
    'best deals',
  ],
};

// Standard breadcrumb patterns
export const breadcrumbPatterns = {
  brand: (brandName: string, brandSlug: string) => [
    { name: 'Home', url: '/' },
    { name: 'Brands', url: '/brands' },
    { name: brandName, url: `/brands/${brandSlug}` },
  ],

  product: (productName: string, productSlug: string, category?: { name: string; slug: string }) => {
    const breadcrumbs = [{ name: 'Home', url: '/' }];
    
    if (category) {
      breadcrumbs.push({
        name: category.name,
        url: `/search?category=${category.slug}`,
      });
    }
    
    breadcrumbs.push({ name: productName, url: `/products/${productSlug}` });
    return breadcrumbs;
  },

  retailer: (retailerName: string, retailerSlug: string) => [
    { name: 'Home', url: '/' },
    { name: 'Retailers', url: '/retailers' },
    { name: retailerName, url: `/retailers/${retailerSlug}` },
  ],

  listing: (listingName: string, listingPath: string) => [
    { name: 'Home', url: '/' },
    { name: listingName, url: listingPath },
  ],
};

// Error handling configuration
export const errorConfig = {
  notFound: {
    title: 'Page Not Found',
    description: 'The requested page could not be found.',
  },
  
  brandNotFound: {
    title: 'Brand Not Found',
    description: 'The requested brand could not be found.',
  },
  
  productNotFound: {
    title: 'Product Not Found',
    description: 'The requested product could not be found.',
  },
  
  retailerNotFound: {
    title: 'Retailer Not Found',
    description: 'The requested retailer could not be found.',
  },
  
  serverError: {
    title: 'Server Error',
    description: 'An unexpected error occurred. Please try again later.',
  },
};

// Page-specific metadata defaults
export const pageDefaults = {
  brand: {
    titleSuffix: 'Promotions & Cashback Deals',
    descriptionTemplate: (name: string) => `Find the latest ${name} cashback offers and promotions.`,
  },
  
  product: {
    titleSuffix: 'Best Cashback Deals',
    descriptionTemplate: (name: string, brandName?: string) => 
      `Get cashback on ${name} from ${brandName || 'top retailers'}. Compare prices and save money with our exclusive offers.`,
  },
  
  retailer: {
    titleSuffix: 'Cashback Deals & Offers',
    descriptionTemplate: (name: string) => 
      `Shop at ${name} and earn cashback on your purchases. Discover exclusive cashback deals and guaranteed rewards.`,
  },
  
  listing: {
    brands: {
      title: 'Shop by Brand | Cashback Deals',
      descriptionTemplate: (count: number) => 
        `Browse our collection of ${count} brands offering exclusive cashback deals and discounts.`,
    },
    products: {
      title: 'All Products | Cashback Deals',
      descriptionTemplate: (page?: number) => 
        page && page > 1 
          ? `Browse all products with cashback offers. Page ${page} of our collection.`
          : 'Browse all products with cashback offers.',
    },
    retailers: {
      title: 'All Retailers | Cashback Deals',
      descriptionTemplate: (count: number) => 
        `Shop at ${count} retailers and earn cashback on your purchases.`,
    },
  },
};