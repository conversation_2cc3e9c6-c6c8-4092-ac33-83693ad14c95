/**
 * SEO Validation Utility
 * Programmatically validates SEO implementation for any page
 */

import { Metadata } from 'next';
import { SITE_URL } from '@/config/domains';

export interface SEOValidationResult {
  isValid: boolean;
  errors: string[];
  warnings: string[];
  score: number; // 0-100
}

export interface SEOValidationOptions {
  strictMode?: boolean;
  checkAbsoluteUrls?: boolean;
  checkStructuredData?: boolean;
  checkMetadata?: boolean;
}

/**
 * Validates metadata object for SEO compliance
 */
export function validateMetadata(
  metadata: Metadata,
  options: SEOValidationOptions = {}
): SEOValidationResult {
  const {
    strictMode = false,
    checkAbsoluteUrls = true,
    checkStructuredData = true,
    checkMetadata = true
  } = options;

  const errors: string[] = [];
  const warnings: string[] = [];
  let score = 100;

  if (checkMetadata) {
    // Title validation
    if (!metadata.title) {
      errors.push('Missing page title');
      score -= 15;
    } else {
      const title = typeof metadata.title === 'string' ? metadata.title : metadata.title.default;
      if (title.length < 30) {
        warnings.push('Page title is too short (< 30 characters)');
        score -= 5;
      } else if (title.length > 60) {
        warnings.push('Page title is too long (> 60 characters)');
        score -= 5;
      }
    }

    // Description validation
    if (!metadata.description) {
      errors.push('Missing meta description');
      score -= 15;
    } else {
      if (metadata.description.length < 120) {
        warnings.push('Meta description is too short (< 120 characters)');
        score -= 5;
      } else if (metadata.description.length > 160) {
        warnings.push('Meta description is too long (> 160 characters)');
        score -= 5;
      }
    }

    // Canonical URL validation
    if (checkAbsoluteUrls && metadata.alternates?.canonical) {
      const canonicalUrl = metadata.alternates.canonical;
      if (!isAbsoluteUrl(canonicalUrl)) {
        errors.push('Canonical URL must be absolute');
        score -= 10;
      } else if (!canonicalUrl.startsWith(SITE_URL)) {
        warnings.push('Canonical URL should use the configured site URL');
        score -= 2;
      }
    } else if (strictMode) {
      errors.push('Missing canonical URL');
      score -= 10;
    }

    // OpenGraph validation
    if (metadata.openGraph) {
      if (metadata.openGraph.url && checkAbsoluteUrls) {
        if (!isAbsoluteUrl(metadata.openGraph.url)) {
          errors.push('OpenGraph URL must be absolute');
          score -= 8;
        }

        // Check URL consistency
        if (metadata.alternates?.canonical && metadata.openGraph.url !== metadata.alternates.canonical) {
          errors.push('OpenGraph URL and canonical URL must be identical');
          score -= 10;
        }
      }

      if (!metadata.openGraph.title) {
        warnings.push('Missing OpenGraph title');
        score -= 3;
      }

      if (!metadata.openGraph.description) {
        warnings.push('Missing OpenGraph description');
        score -= 3;
      }
    } else if (strictMode) {
      warnings.push('Missing OpenGraph data');
      score -= 5;
    }

    // Twitter validation
    if (metadata.twitter) {
      if (!metadata.twitter.title) {
        warnings.push('Missing Twitter title');
        score -= 2;
      }

      if (!metadata.twitter.description) {
        warnings.push('Missing Twitter description');
        score -= 2;
      }
    }
  }

  return {
    isValid: errors.length === 0,
    errors,
    warnings,
    score: Math.max(0, score)
  };
}

/**
 * Validates structured data (JSON-LD)
 */
export function validateStructuredData(
  jsonLdScripts: string[],
  expectedTypes: string[] = []
): SEOValidationResult {
  const errors: string[] = [];
  const warnings: string[] = [];
  let score = 100;

  if (jsonLdScripts.length === 0) {
    errors.push('No JSON-LD structured data found');
    score -= 20;
  }

  jsonLdScripts.forEach((scriptContent, index) => {
    try {
      const data = JSON.parse(scriptContent);
      
      // Validate basic structure
      if (!data['@context']) {
        errors.push(`JSON-LD script ${index + 1}: Missing @context`);
        score -= 5;
      } else if (data['@context'] !== 'https://schema.org') {
        warnings.push(`JSON-LD script ${index + 1}: @context should be 'https://schema.org'`);
        score -= 2;
      }

      if (!data['@type']) {
        errors.push(`JSON-LD script ${index + 1}: Missing @type`);
        score -= 5;
      }

      if (!data.name && data['@type'] !== 'BreadcrumbList') {
        warnings.push(`JSON-LD script ${index + 1}: Missing name property`);
        score -= 2;
      }

      // Validate URLs are absolute
      if (data.url && !isAbsoluteUrl(data.url)) {
        errors.push(`JSON-LD script ${index + 1}: URL must be absolute`);
        score -= 3;
      }

      // Validate against expected types
      if (expectedTypes.length > 0 && !expectedTypes.includes(data['@type'])) {
        warnings.push(`JSON-LD script ${index + 1}: Unexpected type '${data['@type']}', expected one of: ${expectedTypes.join(', ')}`);
        score -= 2;
      }

    } catch (e) {
      errors.push(`JSON-LD script ${index + 1}: Invalid JSON`);
      score -= 10;
    }
  });

  return {
    isValid: errors.length === 0,
    errors,
    warnings,
    score: Math.max(0, score)
  };
}

/**
 * Comprehensive SEO validation for a complete page
 */
export function validatePageSEO(
  metadata: Metadata,
  jsonLdScripts: string[] = [],
  expectedSchemaTypes: string[] = [],
  options: SEOValidationOptions = {}
): SEOValidationResult {
  const metadataResult = validateMetadata(metadata, options);
  const structuredDataResult = validateStructuredData(jsonLdScripts, expectedSchemaTypes);

  const combinedErrors = [...metadataResult.errors, ...structuredDataResult.errors];
  const combinedWarnings = [...metadataResult.warnings, ...structuredDataResult.warnings];
  
  // Calculate weighted score (metadata 60%, structured data 40%)
  const combinedScore = Math.round(
    (metadataResult.score * 0.6) + (structuredDataResult.score * 0.4)
  );

  return {
    isValid: combinedErrors.length === 0,
    errors: combinedErrors,
    warnings: combinedWarnings,
    score: combinedScore
  };
}

/**
 * Helper function to check if a URL is absolute
 */
function isAbsoluteUrl(url: string): boolean {
  return /^https?:\/\//.test(url);
}

/**
 * Generate SEO validation report
 */
export function generateSEOReport(result: SEOValidationResult): string {
  const { isValid, errors, warnings, score } = result;
  
  let report = `\n🔍 SEO Validation Report\n`;
  report += `═══════════════════════\n`;
  report += `Status: ${isValid ? '✅ PASSED' : '❌ FAILED'}\n`;
  report += `Score: ${score}/100\n`;
  
  if (score >= 90) {
    report += `Grade: A+ (Excellent)\n`;
  } else if (score >= 80) {
    report += `Grade: A (Good)\n`;
  } else if (score >= 70) {
    report += `Grade: B (Fair)\n`;
  } else if (score >= 60) {
    report += `Grade: C (Poor)\n`;
  } else {
    report += `Grade: F (Failing)\n`;
  }
  
  if (errors.length > 0) {
    report += `\n🚨 Errors (${errors.length}):\n`;
    errors.forEach((error, index) => {
      report += `  ${index + 1}. ${error}\n`;
    });
  }
  
  if (warnings.length > 0) {
    report += `\n⚠️  Warnings (${warnings.length}):\n`;
    warnings.forEach((warning, index) => {
      report += `  ${index + 1}. ${warning}\n`;
    });
  }
  
  if (errors.length === 0 && warnings.length === 0) {
    report += `\n🎉 No issues found!\n`;
  }
  
  report += `\n`;
  
  return report;
}

/**
 * SEO validation presets for different page types
 */
export const SEO_VALIDATION_PRESETS = {
  brand: {
    expectedSchemaTypes: ['Brand', 'BreadcrumbList'],
    strictMode: true,
    checkAbsoluteUrls: true,
    checkStructuredData: true,
    checkMetadata: true
  },
  product: {
    expectedSchemaTypes: ['Product', 'BreadcrumbList'],
    strictMode: true,
    checkAbsoluteUrls: true,
    checkStructuredData: true,
    checkMetadata: true
  },
  retailer: {
    expectedSchemaTypes: ['Organization', 'BreadcrumbList'],
    strictMode: true,
    checkAbsoluteUrls: true,
    checkStructuredData: true,
    checkMetadata: true
  },
  listing: {
    expectedSchemaTypes: ['CollectionPage', 'BreadcrumbList'],
    strictMode: false,
    checkAbsoluteUrls: true,
    checkStructuredData: true,
    checkMetadata: true
  }
};