// Comprehensive SEO Utilities
// High-level functions that combine metadata-utils and seo-config for consistent SEO

import { Metadata } from 'next';
import { 
  constructMetadata, 
  createErrorMetadata, 
  isValidUUID,
  type MetadataOptions 
} from './metadata-utils';
import {
  structuredDataTemplates,
  openGraphTemplates,
  keywordTemplates,
  breadcrumbPatterns,
  errorConfig,
  pageDefaults,
  type PageType,
} from './seo-config';
import { SITE_URL } from '@/config/domains';

// Generic entity interface for SEO data
interface SEOEntity {
  id: string;
  name: string;
  slug?: string | null;
  description?: string | null;
  logoUrl?: string | null;
  websiteUrl?: string | null;
}

// Product-specific interface
interface SEOProduct extends SEOEntity {
  images?: string[] | null;
  brand?: { name: string; logoUrl?: string | null } | null;
  category?: { name: string; slug: string } | null;
  modelNumber?: string | null;
  retailerOffers?: any[];
}

// SEO generation result
interface SEOResult {
  metadata: Metadata;
  structuredData: {
    entity?: any;
    breadcrumbs?: any;
  };
  breadcrumbItems: Array<{ name: string; url: string }>;
}

// Error handling wrapper for SEO operations
export async function withSEOErrorHandling<T>(
  operation: () => Promise<T>,
  pageType: PageType,
  fallbackTitle?: string
): Promise<{ data: T | null; errorMetadata: Metadata | null }> {
  try {
    const data = await operation();
    return { data, errorMetadata: null };
  } catch (error) {
    console.error(`SEO Error in ${pageType} page:`, error);
    
    const errorKey = `${pageType}NotFound` as keyof typeof errorConfig;
    const errorInfo = errorConfig[errorKey] || errorConfig.notFound;
    
    return {
      data: null,
      errorMetadata: createErrorMetadata(
        fallbackTitle || errorInfo.title,
        errorInfo.description
      ),
    };
  }
}

// Generate complete SEO for brand pages
export function generateBrandSEO(brand: SEOEntity): SEOResult {
  const slug = brand.slug || brand.id;
  const pathname = `/brands/${slug}`;
  const canonicalUrl = `${SITE_URL}${pathname}`;
  
  // Generate metadata
  const metadata = constructMetadata({
    title: `${brand.name} ${pageDefaults.brand.titleSuffix}`,
    description: brand.description || pageDefaults.brand.descriptionTemplate(brand.name),
    image: brand.logoUrl || undefined,
    pathname,
    keywords: keywordTemplates.brand(brand.name),
    pageType: 'website',
    openGraph: {
      ...openGraphTemplates.brand,
      url: canonicalUrl,
    },
  });

  // Generate structured data
  const entityStructuredData = structuredDataTemplates.brand({
    ...brand,
    canonicalUrl,
  });

  const breadcrumbItems = breadcrumbPatterns.brand(brand.name, slug);
  const breadcrumbStructuredData = structuredDataTemplates.breadcrumbList(breadcrumbItems);

  return {
    metadata,
    structuredData: {
      entity: entityStructuredData,
      breadcrumbs: breadcrumbStructuredData,
    },
    breadcrumbItems,
  };
}

// Generate complete SEO for product pages
export function generateProductSEO(product: SEOProduct): SEOResult {
  const slug = product.slug || product.id;
  const pathname = `/products/${slug}`;
  const canonicalUrl = `${SITE_URL}${pathname}`;
  
  // Prepare product data for structured data
  const productForSchema = {
    ...product,
    images: product.images || undefined,
    brand: product.brand || undefined,
    category: product.category?.name || undefined,
    modelNumber: product.modelNumber || undefined,
    slug,
    offers: product.retailerOffers || [],
  };

  // Generate metadata
  const metadata = constructMetadata({
    title: `${product.name} - ${pageDefaults.product.titleSuffix}`,
    description: product.description?.substring(0, 155) + '...' || 
                 pageDefaults.product.descriptionTemplate(product.name, product.brand?.name),
    image: product.images?.[0] || product.brand?.logoUrl || undefined,
    pathname,
    keywords: keywordTemplates.product(product.name, product.brand?.name),
    pageType: 'website',
    openGraph: {
      ...openGraphTemplates.product,
      url: canonicalUrl,
    },
  });

  // Generate structured data
  const entityStructuredData = structuredDataTemplates.product(productForSchema);
  
  const breadcrumbItems = breadcrumbPatterns.product(
    product.name, 
    slug, 
    product.category || undefined
  );
  const breadcrumbStructuredData = structuredDataTemplates.breadcrumbList(breadcrumbItems);

  return {
    metadata,
    structuredData: {
      entity: entityStructuredData,
      breadcrumbs: breadcrumbStructuredData,
    },
    breadcrumbItems,
  };
}

// Generate complete SEO for retailer pages
export function generateRetailerSEO(retailer: SEOEntity): SEOResult {
  const slug = retailer.slug || retailer.id;
  const pathname = `/retailers/${slug}`;
  const canonicalUrl = `${SITE_URL}${pathname}`;

  // Generate metadata
  const metadata = constructMetadata({
    title: `${retailer.name} ${pageDefaults.retailer.titleSuffix}`,
    description: retailer.description?.substring(0, 155) + '...' || 
                 pageDefaults.retailer.descriptionTemplate(retailer.name),
    image: retailer.logoUrl || undefined,
    pathname,
    keywords: keywordTemplates.retailer(retailer.name),
    pageType: 'website',
    openGraph: {
      ...openGraphTemplates.standard,
      url: canonicalUrl,
    },
  });

  // Generate structured data
  const entityStructuredData = structuredDataTemplates.organization(retailer);
  
  const breadcrumbItems = breadcrumbPatterns.retailer(retailer.name, slug);
  const breadcrumbStructuredData = structuredDataTemplates.breadcrumbList(breadcrumbItems);

  return {
    metadata,
    structuredData: {
      entity: entityStructuredData,
      breadcrumbs: breadcrumbStructuredData,
    },
    breadcrumbItems,
  };
}

// Generate SEO for listing pages with enhanced functionality
export function generateListingSEO(
  type: 'brands' | 'products' | 'retailers',
  options: {
    page?: number;
    itemCount?: number;
    search?: string;
    filters?: Record<string, string>;
    totalPages?: number;
    hasNext?: boolean;
    hasPrev?: boolean;
  } = {}
): SEOResult {
  const { page = 1, itemCount = 0, search, filters, totalPages, hasNext, hasPrev } = options;

  const pathname = `/${type}`;
  const canonicalUrl = `${SITE_URL}${pathname}`;

  // Build title and description based on type
  const config = pageDefaults.listing[type];
  let title = config.title;
  let description: string;

  // Handle different description templates
  if (type === 'products') {
    description = config.descriptionTemplate(page);
  } else {
    description = config.descriptionTemplate(itemCount);
  }

  // Modify for pagination
  if (page > 1) {
    title = title.replace('|', `- Page ${page} |`);
    if (!description.includes('Page')) {
      description = `${description} Page ${page}.`;
    }
  }

  // Modify for search
  if (search) {
    title = `Search: "${search}" | ${type.charAt(0).toUpperCase() + type.slice(1)}`;
    description = `Search results for "${search}" in ${type}.`;
  }

  // Build canonical URL with pagination
  let canonicalPath = pathname;
  if (page > 1) {
    canonicalPath = `${pathname}?page=${page}`;
  }

  // Generate metadata with pagination support
  const metadata = constructMetadata({
    title,
    description,
    pathname: canonicalPath,
    keywords: keywordTemplates.listing(type),
    pageType: 'website',
    openGraph: {
      ...openGraphTemplates.listing,
      url: canonicalUrl,
    },
  });

  // Add pagination links to metadata
  if (hasPrev && page > 1) {
    const prevUrl = page === 2 ? canonicalUrl : `${canonicalUrl}?page=${page - 1}`;
    if (!metadata.other) metadata.other = {};
    metadata.other['link:prev'] = prevUrl;
  }

  if (hasNext && totalPages && page < totalPages) {
    const nextUrl = `${canonicalUrl}?page=${page + 1}`;
    if (!metadata.other) metadata.other = {};
    metadata.other['link:next'] = nextUrl;
  }

  // Generate structured data
  const collectionData = structuredDataTemplates.collectionPage({
    name: title,
    description,
    url: canonicalUrl,
    itemCount,
    itemType: type === 'brands' ? 'Brand' : type === 'products' ? 'Product' : 'Organization',
  });

  const breadcrumbItems = breadcrumbPatterns.listing(
    type.charAt(0).toUpperCase() + type.slice(1),
    pathname
  );
  const breadcrumbStructuredData = structuredDataTemplates.breadcrumbList(breadcrumbItems);

  return {
    metadata,
    structuredData: {
      entity: collectionData,
      breadcrumbs: breadcrumbStructuredData,
    },
    breadcrumbItems,
  };
}

// Generate SEO for search pages
export function generateSearchSEO(options: {
  query?: string;
  page?: number;
  category?: string;
  subcategory?: string;
  brand?: string;
  totalResults?: number;
}): SEOResult {
  const { query, page = 1, category, subcategory, brand, totalResults = 0 } = options;

  const pathname = '/search';
  const canonicalUrl = `${SITE_URL}${pathname}`;

  // Build title and description based on search parameters
  let title = 'Search Products - Find Cashback Deals';
  let description = 'Search for products and discover cashback deals from top brands. Find the best offers and save money on your purchases.';

  if (query) {
    title = `"${query}" - Search Results`;
    description = `Search results for "${query}". Find cashback deals and exclusive offers on ${query} from top retailers.`;
  }

  if (category) {
    const categoryText = subcategory ? `${category} > ${subcategory}` : category;
    title = `${categoryText} - Cashback Deals`;
    description = `Discover cashback deals and offers in ${categoryText}. Save money on your purchases with exclusive rebates and rewards.`;
  }

  // Add pagination to title if needed
  if (page > 1) {
    title = title.replace(' - ', ` - Page ${page} - `);
    description = `${description} Page ${page}.`;
  }

  // Build search URL with parameters
  let searchPath = pathname;
  const searchParams = new URLSearchParams();
  if (query) searchParams.set('q', query);
  if (category) searchParams.set('category', category);
  if (subcategory) searchParams.set('subcategory', subcategory);
  if (brand) searchParams.set('brand', brand);
  if (page > 1) searchParams.set('page', page.toString());

  if (searchParams.toString()) {
    searchPath = `${pathname}?${searchParams.toString()}`;
  }

  // Generate keywords
  const keywords = [query, category, subcategory, brand, 'cashback', 'deals', 'offers', 'search', 'products', 'save money']
    .filter((k): k is string => Boolean(k));

  // Generate metadata
  const metadata = constructMetadata({
    title,
    description,
    pathname: searchPath,
    keywords,
    pageType: 'website',
    openGraph: {
      ...openGraphTemplates.standard,
      url: canonicalUrl,
    },
  });

  // Generate structured data for search results
  const searchData = {
    '@context': 'https://schema.org',
    '@type': 'SearchResultsPage',
    name: title,
    description,
    url: canonicalUrl,
    mainEntity: {
      '@type': 'ItemList',
      numberOfItems: totalResults,
    },
    potentialAction: {
      '@type': 'SearchAction',
      target: `${canonicalUrl}?q={search_term_string}`,
      'query-input': 'required name=search_term_string',
    },
  };

  const breadcrumbItems = [
    { name: 'Home', url: '/' },
    { name: 'Search', url: pathname },
  ];

  if (query) {
    breadcrumbItems.push({ name: `"${query}"`, url: searchPath });
  }

  const breadcrumbStructuredData = structuredDataTemplates.breadcrumbList(breadcrumbItems);

  return {
    metadata,
    structuredData: {
      entity: searchData,
      breadcrumbs: breadcrumbStructuredData,
    },
    breadcrumbItems,
  };
}

// Generate SEO for homepage
export function generateHomepageSEO(): SEOResult {

  const pathname = '/';
  const canonicalUrl = SITE_URL;

  const title = 'Best Cashback Deals & Offers';
  const description = 'Discover the best cashback deals from top retailers. Save money on electronics, fashion, home goods and more with our exclusive offers and rebates.';

  // Generate metadata
  const metadata = constructMetadata({
    title,
    description,
    image: '/og-homepage.jpg',
    pathname,
    keywords: ['cashback', 'deals', 'offers', 'rebates', 'save money', 'discounts', 'rewards'],
    pageType: 'website',
    openGraph: {
      ...openGraphTemplates.standard,
      url: canonicalUrl,
      type: 'website',
    },
  });

  // Generate structured data for homepage
  const websiteData = {
    '@context': 'https://schema.org',
    '@type': 'WebSite',
    name: 'RebateRay',
    alternateName: 'CashbackDeals',
    description,
    url: canonicalUrl,
    potentialAction: {
      '@type': 'SearchAction',
      target: {
        '@type': 'EntryPoint',
        urlTemplate: `${canonicalUrl}search?q={search_term_string}`,
      },
      'query-input': 'required name=search_term_string',
    },
    publisher: {
      '@type': 'Organization',
      name: 'RebateRay',
      url: canonicalUrl,
    },
  };

  const breadcrumbItems = [{ name: 'Home', url: '/' }];
  const breadcrumbStructuredData = structuredDataTemplates.breadcrumbList(breadcrumbItems);

  return {
    metadata,
    structuredData: {
      entity: websiteData,
      breadcrumbs: breadcrumbStructuredData,
    },
    breadcrumbItems,
  };
}

// Utility to determine if an ID is UUID or slug
export function parseEntityId(id: string): { isUUID: boolean; cleanId: string } {
  const cleanId = id.trim();
  return {
    isUUID: isValidUUID(cleanId),
    cleanId,
  };
}

// Generate error metadata for specific page types
export function generateErrorSEO(
  pageType: PageType,
  customTitle?: string,
  customDescription?: string
): Metadata {
  const errorKey = `${pageType}NotFound` as keyof typeof errorConfig;
  const errorInfo = errorConfig[errorKey] || errorConfig.notFound;
  
  return createErrorMetadata(
    customTitle || errorInfo.title,
    customDescription || errorInfo.description
  );
}

// Export commonly used patterns for direct use
export {
  structuredDataTemplates,
  breadcrumbPatterns,
  keywordTemplates,
  openGraphTemplates,
  pageDefaults,
  errorConfig,
};