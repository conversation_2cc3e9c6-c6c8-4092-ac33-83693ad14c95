// Comprehensive SEO Utilities
// High-level functions that combine metadata-utils and seo-config for consistent SEO

import { Metadata } from 'next';
import {
  constructMetadata,
  createErrorMetadata,
  isValidUUID
} from './metadata-utils';
import {
  structuredDataTemplates,
  openGraphTemplates,
  keywordTemplates,
  breadcrumbPatterns,
  errorConfig,
  pageDefaults,
  type PageType,
} from './seo-config';
import { SITE_URL } from '@/config/domains';

// Generic entity interface for SEO data
interface SEOEntity {
  id: string;
  name: string;
  slug?: string | null;
  description?: string | null;
  logoUrl?: string | null;
  websiteUrl?: string | null;
}

// Product-specific interface
interface SEOProduct extends SEOEntity {
  images?: string[] | null;
  brand?: { name: string; logoUrl?: string | null } | null;
  category?: { name: string; slug: string } | null;
  modelNumber?: string | null;
  retailerOffers?: any[];
}

// SEO generation result
interface SEOResult {
  metadata: Metadata;
  structuredData: {
    entity?: any;
    breadcrumbs?: any;
  };
  breadcrumbItems: Array<{ name: string; url: string }>;
}

// Error handling wrapper for SEO operations
export async function withSEOErrorHandling<T>(
  operation: () => Promise<T>,
  pageType: PageType,
  fallbackTitle?: string
): Promise<{ data: T | null; errorMetadata: Metadata | null }> {
  try {
    const data = await operation();
    return { data, errorMetadata: null };
  } catch (error) {
    console.error(`SEO Error in ${pageType} page:`, error);
    
    const errorKey = `${pageType}NotFound` as keyof typeof errorConfig;
    const errorInfo = errorConfig[errorKey] || errorConfig.notFound;
    
    return {
      data: null,
      errorMetadata: createErrorMetadata(
        fallbackTitle || errorInfo.title,
        errorInfo.description
      ),
    };
  }
}

// Generate complete SEO for brand pages
export function generateBrandSEO(brand: SEOEntity): SEOResult {
  const slug = brand.slug || brand.id;
  const pathname = `/brands/${slug}`;
  const canonicalUrl = `${SITE_URL}${pathname}`;
  
  // Generate metadata
  const metadata = constructMetadata({
    title: `${brand.name} ${pageDefaults.brand.titleSuffix}`,
    description: brand.description || pageDefaults.brand.descriptionTemplate(brand.name),
    image: brand.logoUrl || undefined,
    pathname,
    keywords: keywordTemplates.brand(brand.name),
    pageType: 'website',
    openGraph: {
      ...openGraphTemplates.brand,
      url: canonicalUrl,
    },
  });

  // Generate structured data
  const entityStructuredData = structuredDataTemplates.brand({
    ...brand,
    canonicalUrl,
  });

  const breadcrumbItems = breadcrumbPatterns.brand(brand.name, slug);
  const breadcrumbStructuredData = structuredDataTemplates.breadcrumbList(breadcrumbItems);

  return {
    metadata,
    structuredData: {
      entity: entityStructuredData,
      breadcrumbs: breadcrumbStructuredData,
    },
    breadcrumbItems,
  };
}

// Generate complete SEO for product pages
export function generateProductSEO(product: SEOProduct): SEOResult {
  const slug = product.slug || product.id;
  const pathname = `/products/${slug}`;
  const canonicalUrl = `${SITE_URL}${pathname}`;
  
  // Prepare product data for structured data
  const productForSchema = {
    ...product,
    images: product.images || undefined,
    brand: product.brand || undefined,
    category: product.category?.name || undefined,
    modelNumber: product.modelNumber || undefined,
    slug,
    offers: product.retailerOffers || [],
  };

  // Generate metadata
  const metadata = constructMetadata({
    title: `${product.name} - ${pageDefaults.product.titleSuffix}`,
    description: product.description?.substring(0, 155) + '...' || 
                 pageDefaults.product.descriptionTemplate(product.name, product.brand?.name),
    image: product.images?.[0] || product.brand?.logoUrl || undefined,
    pathname,
    keywords: keywordTemplates.product(product.name, product.brand?.name),
    pageType: 'website',
    openGraph: {
      ...openGraphTemplates.product,
      url: canonicalUrl,
    },
  });

  // Generate structured data
  const entityStructuredData = structuredDataTemplates.product(productForSchema);
  
  const breadcrumbItems = breadcrumbPatterns.product(
    product.name, 
    slug, 
    product.category || undefined
  );
  const breadcrumbStructuredData = structuredDataTemplates.breadcrumbList(breadcrumbItems);

  return {
    metadata,
    structuredData: {
      entity: entityStructuredData,
      breadcrumbs: breadcrumbStructuredData,
    },
    breadcrumbItems,
  };
}

// Generate complete SEO for retailer pages
export function generateRetailerSEO(retailer: SEOEntity): SEOResult {
  const slug = retailer.slug || retailer.id;
  const pathname = `/retailers/${slug}`;
  const canonicalUrl = `${SITE_URL}${pathname}`;

  // Generate metadata
  const metadata = constructMetadata({
    title: `${retailer.name} ${pageDefaults.retailer.titleSuffix}`,
    description: retailer.description?.substring(0, 155) + '...' || 
                 pageDefaults.retailer.descriptionTemplate(retailer.name),
    image: retailer.logoUrl || undefined,
    pathname,
    keywords: keywordTemplates.retailer(retailer.name),
    pageType: 'website',
    openGraph: {
      ...openGraphTemplates.standard,
      url: canonicalUrl,
    },
  });

  // Generate structured data
  const entityStructuredData = structuredDataTemplates.organization(retailer);
  
  const breadcrumbItems = breadcrumbPatterns.retailer(retailer.name, slug);
  const breadcrumbStructuredData = structuredDataTemplates.breadcrumbList(breadcrumbItems);

  return {
    metadata,
    structuredData: {
      entity: entityStructuredData,
      breadcrumbs: breadcrumbStructuredData,
    },
    breadcrumbItems,
  };
}

// ADDED: New function to handle listing pages (brands/page.tsx, products/page.tsx, retailers/page.tsx)
// REASON: These pages had inconsistent manual metadata generation - needed unified approach
export function generateListingSEO(
  type: 'brands' | 'products' | 'retailers', // ADDED: Support for all three listing page types
  options: {
    page?: number;           // ADDED: Pagination support for listing pages
    itemCount?: number;      // ADDED: Total item count for SEO descriptions
    search?: string;         // ADDED: Search query support for filtered listings
    filters?: Record<string, string>; // ADDED: Future filter support
    totalPages?: number;     // ADDED: Total pages for pagination metadata
    hasNext?: boolean;       // ADDED: Next page existence for rel="next" links
    hasPrev?: boolean;       // ADDED: Previous page existence for rel="prev" links
  } = {}
): SEOResult {
  // ADDED: Destructure all options with sensible defaults
  const { page = 1, itemCount = 0, search, totalPages, hasNext, hasPrev } = options;

  // ADDED: Build pathname and canonical URL using existing SITE_URL pattern
  const pathname = `/${type}`;
  const canonicalUrl = `${SITE_URL}${pathname}`;

  // ADDED: Use existing pageDefaults.listing config from seo-config.ts
  const config = pageDefaults.listing[type];
  let title = config.title;
  let description: string;

  // ADDED: Handle different description template signatures (products uses page, others use itemCount)
  if (type === 'products') {
    description = config.descriptionTemplate(page); // ADDED: Products page template expects page number
  } else {
    description = config.descriptionTemplate(itemCount); // ADDED: Brands/retailers templates expect item count
  }

  // ADDED: Pagination support in titles and descriptions
  if (page > 1) {
    title = title.replace('|', `- Page ${page} |`); // ADDED: Insert page number before site name
    if (!description.includes('Page')) { // ADDED: Avoid duplicate "Page" text
      description = `${description} Page ${page}.`;
    }
  }

  // ADDED: Search query support for filtered listings
  if (search) {
    title = `Search: "${search}" | ${type.charAt(0).toUpperCase() + type.slice(1)}`;
    description = `Search results for "${search}" in ${type}.`;
  }

  // ADDED: Build canonical path with pagination query parameters
  let canonicalPath = pathname;
  if (page > 1) {
    canonicalPath = `${pathname}?page=${page}`; // ADDED: Include page parameter in canonical URL
  }

  // ADDED: Generate metadata using existing constructMetadata utility
  const metadata = constructMetadata({
    title,
    description,
    pathname: canonicalPath, // ADDED: Use canonical path with pagination
    keywords: keywordTemplates.listing(type), // ADDED: Use existing keyword templates
    pageType: 'website',
    openGraph: {
      ...openGraphTemplates.listing, // ADDED: Use existing OpenGraph templates
      url: canonicalUrl,
    },
  });

  // ADDED: SEO pagination links (rel="prev" and rel="next") for better crawling
  if (hasPrev && page > 1) {
    const prevUrl = page === 2 ? canonicalUrl : `${canonicalUrl}?page=${page - 1}`; // ADDED: Page 2 goes back to base URL
    if (!metadata.other) metadata.other = {}; // ADDED: Initialize metadata.other if needed
    metadata.other['link:prev'] = prevUrl; // ADDED: Add rel="prev" link
  }

  if (hasNext && totalPages && page < totalPages) {
    const nextUrl = `${canonicalUrl}?page=${page + 1}`; // ADDED: Next page URL
    if (!metadata.other) metadata.other = {}; // ADDED: Initialize metadata.other if needed
    metadata.other['link:next'] = nextUrl; // ADDED: Add rel="next" link
  }

  // ADDED: Generate structured data using existing templates
  const collectionData = structuredDataTemplates.collectionPage({
    name: title,
    description,
    url: canonicalUrl,
    itemCount,
    itemType: type === 'brands' ? 'Brand' : type === 'products' ? 'Product' : 'Organization', // ADDED: Map types to Schema.org types
  });

  // ADDED: Generate breadcrumbs using existing patterns
  const breadcrumbItems = breadcrumbPatterns.listing(
    type.charAt(0).toUpperCase() + type.slice(1), // ADDED: Capitalize first letter for breadcrumb labels
    pathname
  );
  const breadcrumbStructuredData = structuredDataTemplates.breadcrumbList(breadcrumbItems);

  // ADDED: Return consistent SEOResult structure matching existing detail page functions
  return {
    metadata,
    structuredData: {
      entity: collectionData,
      breadcrumbs: breadcrumbStructuredData,
    },
    breadcrumbItems,
  };
}

// ADDED: New function to handle search page (search/page.tsx)
// REASON: Search page had complex manual metadata generation - needed unified approach
export function generateSearchSEO(options: {
  query?: string;        // ADDED: Search query parameter
  page?: number;         // ADDED: Pagination support for search results
  category?: string;     // ADDED: Category filter support
  subcategory?: string;  // ADDED: Subcategory filter support
  brand?: string;        // ADDED: Brand filter support
  totalResults?: number; // ADDED: Total search results count for structured data
}): SEOResult {
  // ADDED: Destructure all search parameters with defaults
  const { query, page = 1, category, subcategory, brand, totalResults = 0 } = options;

  // ADDED: Use consistent pathname and URL construction
  const pathname = '/search';
  const canonicalUrl = `${SITE_URL}${pathname}`;

  // ADDED: Build dynamic titles and descriptions based on search context
  let title = 'Search Products - Find Cashback Deals'; // ADDED: Default title for empty search
  let description = 'Search for products and discover cashback deals from top brands. Find the best offers and save money on your purchases.';

  // ADDED: Query-specific title and description
  if (query) {
    title = `"${query}" - Search Results`; // ADDED: Include search query in title
    description = `Search results for "${query}". Find cashback deals and exclusive offers on ${query} from top retailers.`;
  }

  // ADDED: Category-specific title and description (overrides query if both present)
  if (category) {
    const categoryText = subcategory ? `${category} > ${subcategory}` : category; // ADDED: Handle subcategory breadcrumb
    title = `${categoryText} - Cashback Deals`;
    description = `Discover cashback deals and offers in ${categoryText}. Save money on your purchases with exclusive rebates and rewards.`;
  }

  // ADDED: Pagination support in titles
  if (page > 1) {
    title = title.replace(' - ', ` - Page ${page} - `); // ADDED: Insert page number
    description = `${description} Page ${page}.`;
  }

  // ADDED: Build complete search URL with all parameters for canonical
  let searchPath = pathname;
  const searchParams = new URLSearchParams(); // ADDED: Use URLSearchParams for proper encoding
  if (query) searchParams.set('q', query);
  if (category) searchParams.set('category', category);
  if (subcategory) searchParams.set('subcategory', subcategory);
  if (brand) searchParams.set('brand', brand);
  if (page > 1) searchParams.set('page', page.toString());

  // ADDED: Only append query string if parameters exist
  if (searchParams.toString()) {
    searchPath = `${pathname}?${searchParams.toString()}`;
  }

  // ADDED: Generate dynamic keywords based on search context
  const keywords = [query, category, subcategory, brand, 'cashback', 'deals', 'offers', 'search', 'products', 'save money']
    .filter((k): k is string => Boolean(k)); // ADDED: Filter out undefined/null values with proper typing

  // ADDED: Generate metadata using existing constructMetadata utility
  const metadata = constructMetadata({
    title,
    description,
    pathname: searchPath, // ADDED: Use full search path with parameters
    keywords,
    pageType: 'website',
    openGraph: {
      ...openGraphTemplates.standard, // ADDED: Use existing standard OpenGraph template
      url: canonicalUrl,
    },
  });

  // ADDED: Generate SearchResultsPage structured data for better search engine understanding
  const searchData = {
    '@context': 'https://schema.org',
    '@type': 'SearchResultsPage', // ADDED: Specific Schema.org type for search results
    name: title,
    description,
    url: canonicalUrl,
    mainEntity: {
      '@type': 'ItemList', // ADDED: Represent search results as an item list
      numberOfItems: totalResults, // ADDED: Include total results count
    },
    potentialAction: {
      '@type': 'SearchAction', // ADDED: Enable search box in search results
      target: `${canonicalUrl}?q={search_term_string}`,
      'query-input': 'required name=search_term_string',
    },
  };

  // ADDED: Build dynamic breadcrumbs based on search context
  const breadcrumbItems = [
    { name: 'Home', url: '/' },
    { name: 'Search', url: pathname },
  ];

  // ADDED: Add search query to breadcrumbs if present
  if (query) {
    breadcrumbItems.push({ name: `"${query}"`, url: searchPath });
  }

  // ADDED: Generate breadcrumb structured data using existing template
  const breadcrumbStructuredData = structuredDataTemplates.breadcrumbList(breadcrumbItems);

  // ADDED: Return consistent SEOResult structure
  return {
    metadata,
    structuredData: {
      entity: searchData,
      breadcrumbs: breadcrumbStructuredData,
    },
    breadcrumbItems,
  };
}

// ADDED: New function to handle homepage (page.tsx)
// REASON: Homepage had multiple individual structured data components - needed unified approach
export function generateHomepageSEO(options: {
  featuredProductsCount?: number;
  featuredBrandsCount?: number;
  featuredPromotionsCount?: number;
} = {}): SEOResult {
  // ADDED: Destructure options for potential use in SEO descriptions or structured data
  const { featuredProductsCount = 0, featuredBrandsCount = 0, featuredPromotionsCount = 0 } = options;
  // ADDED: Use consistent pathname and URL construction
  const pathname = '/';
  const canonicalUrl = SITE_URL; // ADDED: Use existing SITE_URL constant

  // ADDED: Static homepage title and description (matches existing homepage metadata)
  const title = 'Best Cashback Deals & Offers';

  // ADDED: Dynamic description that can include featured counts if available
  let description = 'Discover the best cashback deals from top retailers. Save money on electronics, fashion, home goods and more with our exclusive offers and rebates.';

  // ADDED: Enhance description with actual counts if provided
  if (featuredProductsCount > 0 || featuredBrandsCount > 0 || featuredPromotionsCount > 0) {
    const parts = [];
    if (featuredProductsCount > 0) parts.push(`${featuredProductsCount} featured products`);
    if (featuredBrandsCount > 0) parts.push(`${featuredBrandsCount} top brands`);
    if (featuredPromotionsCount > 0) parts.push(`${featuredPromotionsCount} exclusive promotions`);

    if (parts.length > 0) {
      description = `Discover the best cashback deals from top retailers. Browse ${parts.join(', ')} and save money on electronics, fashion, home goods and more.`;
    }
  }

  // ADDED: Generate metadata using existing constructMetadata utility
  const metadata = constructMetadata({
    title,
    description,
    image: '/og-homepage.jpg', // ADDED: Homepage-specific OpenGraph image
    pathname,
    keywords: ['cashback', 'deals', 'offers', 'rebates', 'save money', 'discounts', 'rewards'], // ADDED: Homepage-specific keywords
    pageType: 'website',
    openGraph: {
      ...openGraphTemplates.standard, // ADDED: Use existing standard template
      url: canonicalUrl,
      type: 'website', // ADDED: Explicit website type for homepage
    },
  });

  // ADDED: Generate WebSite structured data for homepage (replaces existing WebSiteStructuredData component)
  const websiteData = {
    '@context': 'https://schema.org',
    '@type': 'WebSite', // ADDED: Schema.org WebSite type for homepage
    name: 'RebateRay', // ADDED: Site name
    alternateName: 'CashbackDeals', // ADDED: Alternative site name
    description,
    url: canonicalUrl,
    potentialAction: {
      '@type': 'SearchAction', // ADDED: Enable site search in search engines
      target: {
        '@type': 'EntryPoint',
        urlTemplate: `${canonicalUrl}search?q={search_term_string}`, // ADDED: Search URL template
      },
      'query-input': 'required name=search_term_string',
    },
    publisher: {
      '@type': 'Organization', // ADDED: Publisher information
      name: 'RebateRay',
      url: canonicalUrl,
    },
  };

  // ADDED: Simple homepage breadcrumb (just Home)
  const breadcrumbItems = [{ name: 'Home', url: '/' }];
  const breadcrumbStructuredData = structuredDataTemplates.breadcrumbList(breadcrumbItems);

  // ADDED: Return consistent SEOResult structure
  return {
    metadata,
    structuredData: {
      entity: websiteData,
      breadcrumbs: breadcrumbStructuredData,
    },
    breadcrumbItems,
  };
}

// Utility to determine if an ID is UUID or slug
export function parseEntityId(id: string): { isUUID: boolean; cleanId: string } {
  const cleanId = id.trim();
  return {
    isUUID: isValidUUID(cleanId),
    cleanId,
  };
}

// Generate error metadata for specific page types
export function generateErrorSEO(
  pageType: PageType,
  customTitle?: string,
  customDescription?: string
): Metadata {
  const errorKey = `${pageType}NotFound` as keyof typeof errorConfig;
  const errorInfo = errorConfig[errorKey] || errorConfig.notFound;
  
  return createErrorMetadata(
    customTitle || errorInfo.title,
    customDescription || errorInfo.description
  );
}

// Export commonly used patterns for direct use
export {
  structuredDataTemplates,
  breadcrumbPatterns,
  keywordTemplates,
  openGraphTemplates,
  pageDefaults,
  errorConfig,
};