// src/components/seo/StructuredData.tsx
// This component generates JSON-LD structured data for products and product lists
// to improve SEO and enable rich snippets in search results

'use client';

import React from 'react';
import { Product } from '@/types/product';
import { TransformedProduct, TransformedRetailerOffer } from '@/lib/data/types';
import { renderSecureJsonLd } from '@/lib/security/utils';
import { SITE_URL } from '@/config/domains';

interface ProductStructuredDataProps {
  product: TransformedProduct;
  retailerOffers?: TransformedRetailerOffer[];
  fallbackPurchaseEndDate?: string;
}

export const ProductStructuredData: React.FC<ProductStructuredDataProps> = ({
  product,
  retailerOffers,
  fallbackPurchaseEndDate
}) => {
  // Function to get full image URL
  const getFullImageUrl = (image: string) => {
    if (!image) return null;

    // If it's already a full URL, return it as is
    if (image.startsWith('http')) {
      return image;
    }

    // Construct the Supabase storage URL if needed
    return `${process.env.NEXT_PUBLIC_SUPABASE_URL}/storage/v1/object/public/${image}`;
  };

  // Enhanced availability status mapping for better rich snippets
  const getEnhancedAvailability = (stockStatus: string | null): string => {
    switch (stockStatus?.toLowerCase()) {
      case 'in_stock':
      case 'available':
      case 'in stock':
        return 'https://schema.org/InStock';
      case 'out_of_stock':
      case 'out of stock':
      case 'unavailable':
        return 'https://schema.org/OutOfStock';
      case 'limited_stock':
      case 'limited stock':
      case 'low_stock':
      case 'low stock':
        return 'https://schema.org/LimitedAvailability';
      case 'preorder':
      case 'pre-order':
      case 'pre_order':
        return 'https://schema.org/PreOrder';
      case 'discontinued':
        return 'https://schema.org/Discontinued';
      case 'sold_out':
      case 'sold out':
        return 'https://schema.org/SoldOut';
      default:
        // Default to InStock for better rich snippet visibility unless explicitly unavailable
        return stockStatus ? 'https://schema.org/LimitedAvailability' : 'https://schema.org/InStock';
    }
  };

  // Currency validation and formatting
  const formatPrice = (price: number | null): string | null => {
    if (typeof price !== 'number' || price <= 0 || !isFinite(price)) {
      return null;
    }
    // Format to 2 decimal places for currency
    return price.toFixed(2);
  };

  // Enhanced price validation with better filtering
  const isValidPrice = (price: number | null): price is number => {
    return typeof price === 'number' && price > 0 && isFinite(price);
  };

  // Get all image URLs
  const imageUrls = product.images && product.images.length > 0
    ? product.images.map(img => getFullImageUrl(img)).filter(Boolean)
    : [];

  // Add brand logo if available and no product images
  if (imageUrls.length === 0 && product.brand?.logoUrl) {
    imageUrls.push(product.brand.logoUrl);
  }

  // Use retailerOffers prop or fallback to product.retailerOffers
  const offers = retailerOffers || product.retailerOffers || [];
  
  // Enhanced offer validation with better price filtering
  const validOffers = offers.filter(offer => {
    return isValidPrice(offer.price) && offer.retailer?.name; // Must have valid price and retailer
  });

  // Calculate price range for AggregateOffer with enhanced validation
  const prices = validOffers
    .map(offer => offer.price as number)
    .filter(isValidPrice);
    
  const minPrice = prices.length > 0 ? Math.min(...prices) : null;
  const maxPrice = prices.length > 0 ? Math.max(...prices) : null;

  // Generate priceValidUntil from promotion data or fallback
  const getPriceValidUntil = (): string | undefined => {
    // Priority: promotion end date, then fallback date
    const endDate = product.promotion?.purchaseEndDate || fallbackPurchaseEndDate;
    if (!endDate) return undefined;
    
    // Extract date part (YYYY-MM-DD) from ISO date string
    return endDate.split('T')[0];
  };

  // Enhanced structured data according to Schema.org's Product schema with rich snippets support
  const structuredData = {
    '@context': 'https://schema.org',
    '@type': 'Product',
    name: product.name,
    description: product.description || `${product.name} with cashback offer`,
    image: imageUrls.length > 0 ? imageUrls : undefined,
    sku: product.modelNumber || product.id,
    mpn: product.modelNumber,
    // Add GTIN if available (for better Google Shopping integration)
    ...(product.modelNumber && { gtin: product.modelNumber }),
    // Add product URL for merchant listings
    url: `${SITE_URL}/products/${product.slug || product.id}`,

    // Enhanced Brand schema
    brand: product.brand ? {
      '@type': 'Brand',
      name: product.brand.name,
      logo: product.brand.logoUrl,
      description: product.brand.description
    } : undefined,

    // Category information
    category: product.category?.name,

    // Only include ratings/reviews if we have actual review data in our database
    // TODO: Add aggregateRating and review fields when we have real review data

    // Enhanced individual offers from retailers for better rich snippets
    offers: validOffers.length > 0 ? validOffers.map(offer => {
      const formattedPrice = formatPrice(offer.price);
      if (!formattedPrice) return null; // Skip invalid prices
      
      return {
        '@type': 'Offer',
        url: offer.url || `${SITE_URL}/products/${product.slug || product.id}`,
        price: formattedPrice,
        priceCurrency: 'GBP',
        priceValidUntil: getPriceValidUntil(),
        availability: getEnhancedAvailability(offer.stockStatus),
        itemCondition: 'https://schema.org/NewCondition',
        // Only include merchant policies if we have actual retailer policy data
        // TODO: Add returnPolicy and shippingDetails when we have real retailer data
        seller: {
          '@type': 'Organization',
          name: offer.retailer?.name || 'Unknown Retailer',
          ...(offer.retailer?.websiteUrl && { url: offer.retailer.websiteUrl }),
          // Enhanced organization details for better SEO
          ...(offer.retailer?.websiteUrl && {
            '@id': offer.retailer.websiteUrl,
            identifier: offer.retailer.id
          })
        }
      };
    }).filter(Boolean) : undefined,

    // Enhanced AggregateOffer for "from £X" pricing in rich snippets
    ...(validOffers.length > 1 && minPrice && maxPrice ? {
      aggregateOffer: {
        '@type': 'AggregateOffer',
        lowPrice: formatPrice(minPrice) || minPrice.toString(),
        highPrice: formatPrice(maxPrice) || maxPrice.toString(),
        priceCurrency: 'GBP',
        offerCount: validOffers.length.toString(),
        // Use most favorable availability status for aggregate
        availability: validOffers.some(offer => 
          getEnhancedAvailability(offer.stockStatus) === 'https://schema.org/InStock'
        ) ? 'https://schema.org/InStock' : 'https://schema.org/LimitedAvailability',
        // Add price validation date for aggregate offer
        ...(getPriceValidUntil() && { priceValidUntil: getPriceValidUntil() }),
        // Enhanced aggregate seller information
        ...(validOffers.length > 0 && {
          seller: validOffers.map(offer => ({
            '@type': 'Organization',
            name: offer.retailer?.name || 'Unknown Retailer',
            ...(offer.retailer?.websiteUrl && { url: offer.retailer.websiteUrl })
          }))
        })
      }
    } : validOffers.length === 1 && minPrice ? {
      // Single offer should still have aggregate structure for consistent pricing display
      aggregateOffer: {
        '@type': 'AggregateOffer',
        lowPrice: formatPrice(minPrice) || minPrice.toString(),
        highPrice: formatPrice(minPrice) || minPrice.toString(),
        priceCurrency: 'GBP',
        offerCount: '1',
        availability: getEnhancedAvailability(validOffers[0].stockStatus),
        ...(getPriceValidUntil() && { priceValidUntil: getPriceValidUntil() })
      }
    } : {}),

    // Additional product information for rich snippets
    ...(product.isFeatured ? {
      additionalProperty: {
        '@type': 'PropertyValue',
        name: 'Featured Product',
        value: 'true'
      }
    } : {}),

    // Geographic audience targeting (UK market focus)
    audience: {
      '@type': 'Audience',
      geographicArea: {
        '@type': 'Country',
        name: 'United Kingdom'
      }
    },

    // Cashback promotion as additional offer with enhanced BuyAction
    ...(product.cashbackAmount && product.cashbackAmount > 0 ? {
      potentialAction: {
        '@type': 'BuyAction',
        target: validOffers.length > 0 ? validOffers[0].url : undefined,
        priceSpecification: {
          '@type': 'PriceSpecification',
          price: product.cashbackAmount.toString(),
          priceCurrency: 'GBP',
          name: 'Cashback Amount'
        },
        // Cashback offer details (only if we have actual cashback amount)
        ...(product.cashbackAmount && {
          expectsAcceptanceOf: {
            '@type': 'Offer',
            name: 'Cashback Offer',
            description: `Get £${product.cashbackAmount} cashback on this purchase`,
            priceSpecification: {
              '@type': 'PriceSpecification',
              price: `-${product.cashbackAmount}`,
              priceCurrency: 'GBP'
            }
          }
        })
      }
    } : {})
  };

  return (
    <script
      type="application/ld+json"
      dangerouslySetInnerHTML={{ __html: renderSecureJsonLd(structuredData) }}
    />
  );
};

// Organization structured data for brands and retailers
interface OrganizationStructuredDataProps {
  organization: {
    id: string;
    name: string;
    logoUrl?: string | null;
    description?: string | null;
    websiteUrl?: string | null;
  };
  organizationType?: 'Brand' | 'Organization';
}

export const OrganizationStructuredData: React.FC<OrganizationStructuredDataProps> = ({
  organization,
  organizationType = 'Organization'
}) => {
  const structuredData = {
    '@context': 'https://schema.org',
    '@type': organizationType,
    name: organization.name,
    description: organization.description,
    logo: organization.logoUrl,
    url: organization.websiteUrl,
    identifier: organization.id
  };

  return (
    <script
      type="application/ld+json"
      dangerouslySetInnerHTML={{ __html: renderSecureJsonLd(structuredData) }}
    />
  );
};

// This component is for generating structured data for a list of products (e.g., on category pages)
interface ProductListStructuredDataProps {
  products: TransformedProduct[];
  listName?: string;
  listDescription?: string;
}

export const ProductListStructuredData: React.FC<ProductListStructuredDataProps> = ({
  products,
  listName = "Products with Cashback Offers",
  listDescription = "Browse our selection of products with cashback offers from top brands"
}) => {
  const structuredData = {
    '@context': 'https://schema.org',
    '@type': 'ItemList',
    name: listName,
    description: listDescription,
    numberOfItems: products.length,
    itemListElement: products.map((product, index) => ({
      '@type': 'ListItem',
      position: index + 1,
      item: {
        '@type': 'Product',
        name: product.name,
        description: product.description || `${product.name} with cashback offer`,
        url: `${SITE_URL}/products/${product.slug || product.id}`,
        image: product.images && product.images.length > 0
          ? product.images[0]?.startsWith('http')
            ? product.images[0]
            : `${process.env.NEXT_PUBLIC_SUPABASE_URL}/storage/v1/object/public/${product.images[0]}`
          : (product.brand?.logoUrl || undefined),
        brand: product.brand ? {
          '@type': 'Brand',
          name: product.brand.name,
          logo: product.brand.logoUrl
        } : undefined,
        sku: product.modelNumber || product.id,
        offers: product.retailerOffers && product.retailerOffers.length > 0 ? (() => {
          const validPrices = product.retailerOffers
            .map(o => o.price)
            .filter((price): price is number => typeof price === 'number' && price > 0 && isFinite(price));
          
          if (validPrices.length === 0) return undefined;
          
          const minPrice = Math.min(...validPrices);
          const maxPrice = Math.max(...validPrices);
          
          return {
            '@type': 'AggregateOffer',
            lowPrice: minPrice.toFixed(2),
            highPrice: maxPrice.toFixed(2),
            priceCurrency: 'GBP',
            offerCount: validPrices.length.toString(),
            availability: 'https://schema.org/InStock'
          };
        })() : undefined
      },
    })),
  };

  return (
    <script
      type="application/ld+json"
      dangerouslySetInnerHTML={{ __html: renderSecureJsonLd(structuredData) }}
    />
  );
};

// WebSite structured data for homepage
export function WebSiteStructuredData() {
  const structuredData = {
    '@context': 'https://schema.org',
    '@type': 'WebSite',
    name: 'RebateRay',
    alternateName: 'CashbackDeals',
    description: 'Discover and compare cashback deals and rebates from top brands in the UK.',
    url: SITE_URL,
    potentialAction: {
      '@type': 'SearchAction',
      target: {
        '@type': 'EntryPoint',
        urlTemplate: `${SITE_URL}/search?q={search_term_string}`
      },
      'query-input': 'required name=search_term_string'
    },
    publisher: {
      '@type': 'Organization',
      name: 'RebateRay',
      url: SITE_URL
    }
  };

  return (
    <script
      type="application/ld+json"
      dangerouslySetInnerHTML={{ __html: renderSecureJsonLd(structuredData) }}
    />
  );
}

// SearchResultsPage structured data for search pages
interface SearchResultsStructuredDataProps {
  query: string;
  results: TransformedProduct[];
  totalResults: number;
}

export const SearchResultsStructuredData: React.FC<SearchResultsStructuredDataProps> = ({
  query,
  results,
  totalResults
}) => {
  const structuredData = {
    '@context': 'https://schema.org',
    '@type': 'SearchResultsPage',
    mainEntity: {
      '@type': 'ItemList',
      numberOfItems: totalResults,
      itemListElement: results.map((product, index) => ({
        '@type': 'ListItem',
        position: index + 1,
        item: {
          '@type': 'Product',
          name: product.name,
          description: product.description,
          image: product.images && product.images.length > 0 ? product.images[0] : undefined,
          url: `${SITE_URL}/products/${product.slug || product.id}`,
          brand: product.brand ? {
            '@type': 'Brand',
            name: product.brand.name
          } : undefined
        }
      }))
    },
    potentialAction: {
      '@type': 'SearchAction',
      target: `/search?q=${encodeURIComponent(query)}`,
      'query-input': 'required name=q'
    }
  };

  return (
    <script
      type="application/ld+json"
      dangerouslySetInnerHTML={{ __html: renderSecureJsonLd(structuredData) }}
    />
  );
};

// BreadcrumbList structured data for navigation paths
interface BreadcrumbStructuredDataProps {
  items: {
    name: string;
    url: string;
  }[];
}

export const BreadcrumbStructuredData: React.FC<BreadcrumbStructuredDataProps> = ({ items }) => {
  const structuredData = {
    '@context': 'https://schema.org',
    '@type': 'BreadcrumbList',
    itemListElement: items.map((item, index) => ({
      '@type': 'ListItem',
      position: index + 1,
      name: item.name,
      item: {
        '@type': 'Thing',
        '@id': item.url,
        name: item.name
      },
    })),
  };

  return (
    <script
      type="application/ld+json"
      dangerouslySetInnerHTML={{ __html: renderSecureJsonLd(structuredData) }}
    />
  );
};

// Enhanced utility function to validate structured data (for development/testing)
export const validateStructuredData = (data: any): boolean => {
  try {
    // Basic validation checks
    if (!data['@context'] || !data['@type']) {
      console.warn('Structured data missing @context or @type');
      return false;
    }

    if (data['@context'] !== 'https://schema.org') {
      console.warn('Structured data should use https://schema.org context');
      return false;
    }

    // Product-specific validation with enhanced checks
    if (data['@type'] === 'Product') {
      const requiredFields = ['name'];
      const recommendedFields = ['description', 'image', 'brand', 'offers'];

      for (const field of requiredFields) {
        if (!data[field]) {
          console.warn(`Product structured data missing required field: ${field}`);
          return false;
        }
      }

      for (const field of recommendedFields) {
        if (!data[field]) {
          console.warn(`Product structured data missing recommended field: ${field}`);
        }
      }

      // Enhanced offer validation
      if (data.offers) {
        const offers = Array.isArray(data.offers) ? data.offers : [data.offers];
        let validOfferCount = 0;
        
        for (const offer of offers) {
          if (offer['@type'] === 'Offer') {
            if (!offer.price || !offer.priceCurrency) {
              console.warn('Offer missing price or priceCurrency');
              continue;
            }
            
            if (!offer.availability || !offer.availability.startsWith('https://schema.org/')) {
              console.warn('Offer missing valid availability schema');
            }
            
            if (!offer.seller || !offer.seller.name) {
              console.warn('Offer missing seller information');
            }

            validOfferCount++;
          }
        }

        if (validOfferCount === 0) {
          console.warn('No valid offers found in product structured data');
        }
      }

      // Validate AggregateOffer if present
      if (data.aggregateOffer) {
        if (!data.aggregateOffer.lowPrice || !data.aggregateOffer.priceCurrency) {
          console.warn('AggregateOffer missing required price fields');
        }
        
        if (!data.aggregateOffer.offerCount) {
          console.warn('AggregateOffer missing offerCount');
        }
      }
    }

    return true;
  } catch (error) {
    console.error('Error validating structured data:', error);
    return false;
  }
};

// New utility function to check if structured data will likely generate rich snippets
export const assessRichSnippetLikelihood = (product: TransformedProduct): {
  likelihood: 'high' | 'medium' | 'low';
  score: number;
  factors: string[];
} => {
  const richSnippetData = generateRichSnippetData(product);
  const score = richSnippetData.richSnippetScore;
  
  const factors = [];
  
  if (richSnippetData.priceRange && richSnippetData.priceRange.count > 1) {
    factors.push('Multiple price offers available');
  }
  
  if (product.promotion?.purchaseEndDate) {
    factors.push('Price validity dates present');
  }
  
  if (product.brand && product.images && product.images.length > 0) {
    factors.push('Complete product information');
  }
  
  if (richSnippetData.missingFields.length === 0) {
    factors.push('All recommended fields present');
  }

  let likelihood: 'high' | 'medium' | 'low' = 'low';
  if (score >= 85) likelihood = 'high';
  else if (score >= 60) likelihood = 'medium';

  return {
    likelihood,
    score,
    factors
  };
};

// Helper function to generate rich snippet optimized structured data with enhanced validation
export const generateRichSnippetData = (product: TransformedProduct) => {
  const offers = product.retailerOffers || [];
  const hasOffers = offers.length > 0;
  
  // Enhanced price validation
  const validPrices = offers
    .map(o => o.price)
    .filter((p): p is number => typeof p === 'number' && p > 0 && isFinite(p));
  
  const hasValidPrices = validPrices.length > 0;
  const hasRetailerInfo = offers.some(o => o.retailer?.name);
  const hasPromotionDates = product.promotion?.purchaseEndDate;
  const hasValidImages = product.images && product.images.length > 0;

  // Enhanced scoring with more criteria
  const baseScore = 
    (product.name ? 15 : 0) +
    (product.description ? 15 : 0) +
    (hasValidImages ? 15 : 0) +
    (product.brand ? 15 : 0) +
    (hasValidPrices ? 15 : 0) +
    (hasRetailerInfo ? 10 : 0) +
    (hasPromotionDates ? 10 : 0) +
    (product.modelNumber ? 5 : 0);

  const missingFields = [
    !product.name && 'name',
    !product.description && 'description',
    !hasValidImages && 'image',
    !product.brand && 'brand',
    !hasValidPrices && 'valid offers with prices',
    !hasRetailerInfo && 'retailer information',
    !hasPromotionDates && 'promotion dates',
    !product.modelNumber && 'model number'
  ].filter(Boolean);

  return {
    hasRichSnippetPotential: hasOffers && hasValidPrices && hasRetailerInfo,
    missingFields,
    richSnippetScore: Math.round(baseScore),
    priceRange: hasValidPrices ? {
      min: Math.min(...validPrices),
      max: Math.max(...validPrices),
      count: validPrices.length
    } : null,
    recommendations: [
      !hasValidPrices && 'Add valid price information for offers',
      !hasRetailerInfo && 'Include retailer information for all offers',
      !hasPromotionDates && 'Add promotion end dates for price validity',
      !product.modelNumber && 'Include product model number for better identification',
      validPrices.length < 2 && hasValidPrices && 'Add multiple retailer offers for "from £X" pricing'
    ].filter(Boolean)
  };
};

// CollectionPage structured data is now generated in the server component