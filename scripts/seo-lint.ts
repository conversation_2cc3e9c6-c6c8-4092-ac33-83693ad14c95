#!/usr/bin/env tsx
/**
 * SEO Linting Script
 * Runs automated SEO validation checks against live pages
 */

import { chromium, Page } from 'playwright';
import chalk from 'chalk';
// Simple SEO validator without complex domain dependencies
interface SEOValidationResult {
  isValid: boolean;
  errors: string[];
  warnings: string[];
  score: number;
}

// Simplified validation presets
const SEO_VALIDATION_PRESETS = {
  brand: { expectedSchemaTypes: ['Brand', 'BreadcrumbList'] },
  product: { expectedSchemaTypes: ['Product', 'BreadcrumbList'] },
  listing: { expectedSchemaTypes: ['CollectionPage', 'BreadcrumbList'] }
};

// Simple validation functions
function validatePageSEO(metadata: any, jsonLdScripts: string[], expectedSchemaTypes: string[]): SEOValidationResult {
  const errors: string[] = [];
  const warnings: string[] = [];
  let score = 100;

  // Basic metadata validation
  if (!metadata.title) { errors.push('Missing title'); score -= 15; }
  if (!metadata.description) { errors.push('Missing description'); score -= 15; }
  if (metadata.alternates?.canonical && !metadata.alternates.canonical.startsWith('http')) {
    errors.push('Canonical URL must be absolute'); score -= 10;
  }

  // JSON-LD validation
  if (jsonLdScripts.length === 0) { errors.push('No JSON-LD found'); score -= 20; }
  
  return { isValid: errors.length === 0, errors, warnings, score };
}

function generateSEOReport(result: SEOValidationResult): string {
  const status = result.isValid ? '✅ PASSED' : '❌ FAILED';
  let report = `Status: ${status} (${result.score}/100)\n`;
  if (result.errors.length > 0) {
    report += `Errors: ${result.errors.join(', ')}\n`;
  }
  return report;
}

interface SEOLintConfig {
  baseUrl: string;
  pages: Array<{
    url: string;
    type: keyof typeof SEO_VALIDATION_PRESETS;
    name: string;
  }>;
}

const config: SEOLintConfig = {
  baseUrl: 'http://localhost:3000',
  pages: [
    { url: '/brands/samsung-uk', type: 'brand', name: 'Samsung UK Brand Page' },
    { url: '/products', type: 'listing', name: 'Products Listing Page' },
    // Add more pages as needed
  ]
};

async function extractSEOData(page: Page) {
  return await page.evaluate(() => {
    const jsonLdScripts = Array.from(document.querySelectorAll('script[type="application/ld+json"]'))
      .map(script => script.textContent || '');
    
    const metadata = {
      title: document.title,
      description: document.querySelector('meta[name="description"]')?.getAttribute('content') || '',
      alternates: {
        canonical: document.querySelector('link[rel="canonical"]')?.getAttribute('href') || undefined
      },
      openGraph: {
        title: document.querySelector('meta[property="og:title"]')?.getAttribute('content') || undefined,
        description: document.querySelector('meta[property="og:description"]')?.getAttribute('content') || undefined,
        url: document.querySelector('meta[property="og:url"]')?.getAttribute('content') || undefined,
        type: document.querySelector('meta[property="og:type"]')?.getAttribute('content') || undefined,
        images: Array.from(document.querySelectorAll('meta[property="og:image"]'))
          .map(meta => ({ url: meta.getAttribute('content') || '' }))
      },
      twitter: {
        title: document.querySelector('meta[name="twitter:title"]')?.getAttribute('content') || undefined,
        description: document.querySelector('meta[name="twitter:description"]')?.getAttribute('content') || undefined,
        card: document.querySelector('meta[name="twitter:card"]')?.getAttribute('content') || undefined,
      }
    };

    return { metadata, jsonLdScripts };
  });
}

async function lintPage(page: Page, pageConfig: SEOLintConfig['pages'][0]) {
  console.log(chalk.blue(`\n📄 Testing: ${pageConfig.name}`));
  console.log(chalk.gray(`   URL: ${config.baseUrl}${pageConfig.url}`));
  
  try {
    await page.goto(`${config.baseUrl}${pageConfig.url}`, { 
      waitUntil: 'networkidle',
      timeout: 30000 
    });
    
    const { metadata, jsonLdScripts } = await extractSEOData(page);
    const validationOptions = SEO_VALIDATION_PRESETS[pageConfig.type];
    
    const result = validatePageSEO(metadata as any, jsonLdScripts, validationOptions.expectedSchemaTypes);
    
    const report = generateSEOReport(result);
    console.log(report);
    
    return result;
    
  } catch (error) {
    console.error(chalk.red(`   ❌ Error testing ${pageConfig.name}:`), error);
    return {
      isValid: false,
      errors: [`Failed to load page: ${error}`],
      warnings: [],
      score: 0
    };
  }
}

async function runSEOLint() {
  console.log(chalk.yellow('🔍 SEO Linting Tool'));
  console.log(chalk.yellow('═══════════════════'));
  console.log(chalk.gray(`Base URL: ${config.baseUrl}`));
  console.log(chalk.gray(`Pages to test: ${config.pages.length}`));

  const browser = await chromium.launch({ headless: true });
  const page = await browser.newPage();
  
  const results = [];
  let totalScore = 0;
  let passedPages = 0;

  for (const pageConfig of config.pages) {
    const result = await lintPage(page, pageConfig);
    results.push({ ...pageConfig, result });
    totalScore += result.score;
    if (result.isValid) passedPages++;
  }

  await browser.close();

  // Summary
  console.log(chalk.yellow('\n📊 SEO Linting Summary'));
  console.log(chalk.yellow('════════════════════'));
  console.log(chalk.gray(`Total pages tested: ${config.pages.length}`));
  console.log(chalk.gray(`Pages passed: ${passedPages}`));
  console.log(chalk.gray(`Pages failed: ${config.pages.length - passedPages}`));
  console.log(chalk.gray(`Average score: ${Math.round(totalScore / config.pages.length)}/100`));

  // Detailed results
  results.forEach(({ name, result }) => {
    const status = result.isValid ? chalk.green('✅ PASS') : chalk.red('❌ FAIL');
    const score = result.score >= 80 ? chalk.green(result.score) : 
                  result.score >= 60 ? chalk.yellow(result.score) : 
                  chalk.red(result.score);
    console.log(`${status} ${name} (${score}/100)`);
  });

  // Exit with appropriate code
  const allPassed = results.every(r => r.result.isValid);
  if (!allPassed) {
    console.log(chalk.red('\n⚠️  Some pages failed SEO validation. Please address the issues above.'));
    process.exit(1);
  } else {
    console.log(chalk.green('\n🎉 All pages passed SEO validation!'));
    process.exit(0);
  }
}

// CLI support for custom URLs
if (process.argv.length > 2) {
  const customUrl = process.argv[2];
  const pageType = (process.argv[3] as keyof typeof SEO_VALIDATION_PRESETS) || 'listing';
  
  config.pages = [{
    url: customUrl.startsWith('/') ? customUrl : `/${customUrl}`,
    type: pageType,
    name: `Custom page: ${customUrl}`
  }];
}

runSEOLint().catch(error => {
  console.error(chalk.red('Fatal error:'), error);
  process.exit(1);
});